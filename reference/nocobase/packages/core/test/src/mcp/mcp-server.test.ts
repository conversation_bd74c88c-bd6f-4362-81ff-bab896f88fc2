import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { createMCPServer, MCPTestUtils } from './mcp-server';
import { MCPPMockServer } from './mcp-server';

describe('MCP Server Tests', () => {
  let app: MCPPMockServer;

  beforeEach(async () => {
    app = await createMCPServer({
      database: {
        dialect: 'sqlite',
        storage: ':memory:',
      },
    });
    await app.loadAndInstall();
  });

  afterEach(async () => {
    if (app) {
      await app.destroy();
    }
  });

  describe('MCP Server Creation', () => {
    it('should create MCP server successfully', async () => {
      const mcpServer = await app.createMCPServer();
      expect(mcpServer).toBeDefined();
      expect(mcpServer.name).toBe('nocobase-mcp-test-server');
    });

    it('should have correct capabilities', async () => {
      const mcpServer = await app.createMCPServer();
      expect(mcpServer.capabilities).toEqual({
        tools: {},
        resources: {},
      });
    });
  });

  describe('MCP Tools Registration', () => {
    beforeEach(async () => {
      await app.createMCPServer();
    });

    it('should register all required tools', async () => {
      const mcpServer = app.getMCPServer();
      const tools = await mcpServer!.listTools();
      
      const toolNames = tools.tools.map((tool: any) => tool.name);
      expect(toolNames).toContain('create_collection');
      expect(toolNames).toContain('query_records');
      expect(toolNames).toContain('create_record');
      expect(toolNames).toContain('update_record');
      expect(toolNames).toContain('delete_record');
    });

    it('should have correct tool schemas', async () => {
      const mcpServer = app.getMCPServer();
      const tools = await mcpServer!.listTools();
      
      const createCollectionTool = tools.tools.find((tool: any) => tool.name === 'create_collection');
      expect(createCollectionTool.inputSchema.properties.name.type).toBe('string');
      expect(createCollectionTool.inputSchema.required).toContain('name');
    });
  });

  describe('Collection Operations', () => {
    beforeEach(async () => {
      await app.createMCPServer();
    });

    it('should create collection successfully', async () => {
      const result = await MCPTestUtils.simulateMCPToolCall(app, 'create_collection', {
        name: 'test_posts',
        fields: [
          {
            type: 'text',
            name: 'title',
          },
          {
            type: 'text',
            name: 'content',
          },
        ],
      });

      expect(result.content[0].text).toContain('Collection \'test_posts\' created successfully');
      
      // 验证集合是否真的创建成功
      const collection = app.db.getCollection('test_posts');
      expect(collection).toBeDefined();
    });

    it('should not create duplicate collection', async () => {
      // 第一次创建
      await MCPTestUtils.simulateMCPToolCall(app, 'create_collection', {
        name: 'test_posts',
        fields: [
          {
            type: 'text',
            name: 'title',
          },
        ],
      });

      // 第二次创建同名集合
      const result = await MCPTestUtils.simulateMCPToolCall(app, 'create_collection', {
        name: 'test_posts',
        fields: [
          {
            type: 'text',
            name: 'title',
          },
        ],
      });

      expect(result.content[0].text).toContain('Collection \'test_posts\' already exists');
    });
  });

  describe('Record Operations', () => {
    beforeEach(async () => {
      await app.createMCPServer();
      // 创建测试集合
      await MCPTestUtils.simulateMCPToolCall(app, 'create_collection', {
        name: 'test_posts',
        fields: [
          {
            type: 'text',
            name: 'title',
          },
          {
            type: 'text',
            name: 'content',
          },
        ],
      });
    });

    it('should create record successfully', async () => {
      const result = await MCPTestUtils.simulateMCPToolCall(app, 'create_record', {
        collection: 'test_posts',
        data: {
          title: 'Test Post',
          content: 'This is a test post',
        },
      });

      const record = JSON.parse(result.content[0].text);
      expect(record.title).toBe('Test Post');
      expect(record.content).toBe('This is a test post');
    });

    it('should query records successfully', async () => {
      // 创建测试数据
      await MCPTestUtils.simulateMCPToolCall(app, 'create_record', {
        collection: 'test_posts',
        data: {
          title: 'Test Post 1',
          content: 'Content 1',
        },
      });

      await MCPTestUtils.simulateMCPToolCall(app, 'create_record', {
        collection: 'test_posts',
        data: {
          title: 'Test Post 2',
          content: 'Content 2',
        },
      });

      // 查询所有记录
      const result = await MCPTestUtils.simulateMCPToolCall(app, 'query_records', {
        collection: 'test_posts',
      });

      const records = JSON.parse(result.content[0].text);
      expect(records.length).toBe(2);
      expect(records[0].title).toBe('Test Post 1');
      expect(records[1].title).toBe('Test Post 2');
    });

    it('should query records with filter', async () => {
      // 创建测试数据
      await MCPTestUtils.simulateMCPToolCall(app, 'create_record', {
        collection: 'test_posts',
        data: {
          title: 'Published Post',
          content: 'Published content',
          status: 'published',
        },
      });

      await MCPTestUtils.simulateMCPToolCall(app, 'create_record', {
        collection: 'test_posts',
        data: {
          title: 'Draft Post',
          content: 'Draft content',
          status: 'draft',
        },
      });

      // 查询已发布的记录
      const result = await MCPTestUtils.simulateMCPToolCall(app, 'query_records', {
        collection: 'test_posts',
        filter: {
          status: 'published',
        },
      });

      const records = JSON.parse(result.content[0].text);
      expect(records.length).toBe(1);
      expect(records[0].title).toBe('Published Post');
    });

    it('should update record successfully', async () => {
      // 创建测试数据
      const createResult = await MCPTestUtils.simulateMCPToolCall(app, 'create_record', {
        collection: 'test_posts',
        data: {
          title: 'Original Title',
          content: 'Original content',
        },
      });

      const createdRecord = JSON.parse(createResult.content[0].text);

      // 更新记录
      const updateResult = await MCPTestUtils.simulateMCPToolCall(app, 'update_record', {
        collection: 'test_posts',
        id: createdRecord.id,
        data: {
          title: 'Updated Title',
          content: 'Updated content',
        },
      });

      const updatedRecord = JSON.parse(updateResult.content[0].text);
      expect(updatedRecord.title).toBe('Updated Title');
      expect(updatedRecord.content).toBe('Updated content');
    });

    it('should delete record successfully', async () => {
      // 创建测试数据
      const createResult = await MCPTestUtils.simulateMCPToolCall(app, 'create_record', {
        collection: 'test_posts',
        data: {
          title: 'To be deleted',
          content: 'This will be deleted',
        },
      });

      const createdRecord = JSON.parse(createResult.content[0].text);

      // 删除记录
      const deleteResult = await MCPTestUtils.simulateMCPToolCall(app, 'delete_record', {
        collection: 'test_posts',
        id: createdRecord.id,
      });

      expect(deleteResult.content[0].text).toContain('deleted successfully');

      // 验证记录已被删除
      const queryResult = await MCPTestUtils.simulateMCPToolCall(app, 'query_records', {
        collection: 'test_posts',
      });

      const records = JSON.parse(queryResult.content[0].text);
      expect(records.length).toBe(0);
    });
  });

  describe('Error Handling', () => {
    beforeEach(async () => {
      await app.createMCPServer();
    });

    it('should handle non-existent collection', async () => {
      const result = await MCPTestUtils.simulateMCPToolCall(app, 'create_record', {
        collection: 'non_existent_collection',
        data: {
          title: 'Test',
        },
      });

      expect(result.isError).toBe(true);
      expect(result.content[0].text).toContain('Collection \'non_existent_collection\' not found');
    });

    it('should handle invalid record ID', async () => {
      // 创建测试集合
      await MCPTestUtils.simulateMCPToolCall(app, 'create_collection', {
        name: 'test_posts',
        fields: [
          {
            type: 'text',
            name: 'title',
          },
        ],
      });

      // 尝试更新不存在的记录
      const result = await MCPTestUtils.simulateMCPToolCall(app, 'update_record', {
        collection: 'test_posts',
        id: 'non-existent-id',
        data: {
          title: 'Updated',
        },
      });

      expect(result.isError).toBe(true);
    });
  });

  describe('Performance Tests', () => {
    beforeEach(async () => {
      await app.createMCPServer();
      await MCPTestUtils.simulateMCPToolCall(app, 'create_collection', {
        name: 'test_posts',
        fields: [
          {
            type: 'text',
            name: 'title',
          },
          {
            type: 'text',
            name: 'content',
          },
        ],
      });
    });

    it('should handle bulk operations efficiently', async () => {
      const startTime = Date.now();
      const batchSize = 100;

      // 批量创建记录
      const createPromises = [];
      for (let i = 0; i < batchSize; i++) {
        createPromises.push(
          MCPTestUtils.simulateMCPToolCall(app, 'create_record', {
            collection: 'test_posts',
            data: {
              title: `Post ${i}`,
              content: `Content ${i}`,
            },
          })
        );
      }

      await Promise.all(createPromises);
      const createDuration = Date.now() - startTime;

      // 验证创建结果
      const queryResult = await MCPTestUtils.simulateMCPToolCall(app, 'query_records', {
        collection: 'test_posts',
      });

      const records = JSON.parse(queryResult.content[0].text);
      expect(records.length).toBe(batchSize);

      console.log(`Created ${batchSize} records in ${createDuration}ms`);
      expect(createDuration).toBeLessThan(5000); // 5秒内完成
    });
  });
});