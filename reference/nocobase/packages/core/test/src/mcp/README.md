# MCP 测试架构

基于 NocoBase 现有测试基础设施的 MCP (Model Context Protocol) 测试解决方案。

## 架构概述

这个测试架构充分利用了 NocoBase 的现有测试基础设施，特别是 `MockServer` 和测试工具链，同时针对 MCP 的特殊需求进行了定制。

### 核心组件

1. **MCPPMockServer** - 扩展的 MockServer，支持 MCP 功能
2. **MCPTestUtils** - MCP 测试工具类
3. **测试套件** - 完整的测试覆盖

## 文件结构

```
packages/core/test/src/mcp/
├── mcp-server.ts              # MCP 服务器实现
├── mcp-server.test.ts         # 基础功能测试
├── integration.test.ts        # 集成测试
├── performance.test.ts        # 性能测试
├── start-test-server.js       # 测试服务器启动脚本
└── test-client.js            # 测试客户端示例
```

## 使用方法

### 1. 运行测试

```bash
# 运行所有 MCP 测试
npm test -- packages/core/test/src/mcp/

# 运行特定测试
npm test -- packages/core/test/src/mcp/mcp-server.test.ts
npm test -- packages/core/test/src/mcp/integration.test.ts
npm test -- packages/core/test/src/mcp/performance.test.ts
```

### 2. 启动测试服务器

```bash
# 启动 MCP 测试服务器
node packages/core/test/src/mcp/start-test-server.js
```

### 3. 运行测试客户端

```bash
# 运行 MCP 测试客户端
node packages/core/test/src/mcp/test-client.js
```

## 功能特性

### 1. 完整的 MCP 工具支持

- **create_collection** - 创建集合
- **query_records** - 查询记录
- **create_record** - 创建记录
- **update_record** - 更新记录
- **delete_record** - 删除记录

### 2. NocoBase 集成

- 与 NocoBase 插件系统兼容
- 支持 ACL 权限控制
- 使用 NocoBase 数据库模型
- 兼容 NocoBase API 风格

### 3. 性能优化

- 批量操作支持
- 并发处理能力
- 内存管理优化
- 查询性能优化

### 4. 测试覆盖

- 单元测试 - 基础功能测试
- 集成测试 - 与 NocoBase 系统集成测试
- 性能测试 - 大规模数据处理测试
- 错误处理测试 - 异常情况处理测试

## 扩展指南

### 添加新的 MCP 工具

1. 在 `MCPPMockServer` 类中添加工具处理方法
2. 在工具注册部分添加新工具
3. 在测试套件中添加相应测试
4. 更新文档和示例

### 自定义测试配置

```typescript
const app = await createMCPServer({
  database: {
    dialect: 'sqlite',
    storage: ':memory:',
  },
  // 其他 NocoBase 配置
});
```

### 性能调优

- 调整批量操作大小
- 优化数据库连接池
- 配置缓存策略
- 监控内存使用

## 依赖要求

- NocoBase 核心包
- MCP SDK
- 测试框架 (Vitest)
- 数据库驱动 (SQLite/PostgreSQL/MySQL)

## 注意事项

1. **测试隔离** - 每个测试使用独立的内存数据库
2. **资源清理** - 测试完成后自动清理资源
3. **错误处理** - 完善的错误处理和日志记录
4. **并发安全** - 支持并发测试执行

## 贡献指南

1. 遵循 NocoBase 代码风格
2. 添加适当的测试覆盖
3. 更新相关文档
4. 确保性能指标达标

这个测试架构为 MCP 在 NocoBase 中的实现提供了完整的测试保障，确保了功能的正确性和系统的稳定性。