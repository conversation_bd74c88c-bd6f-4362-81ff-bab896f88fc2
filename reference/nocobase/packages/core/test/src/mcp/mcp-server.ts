import { MockServer, createMockServer } from '@nocobase/test';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { Tool, ListToolsRequestSchema, CallToolRequestSchema } from '@modelcontextprotocol/sdk/types.js';

/**
 * MCP 测试专用的 Mock Server 扩展
 */
export class MCPPMockServer extends MockServer {
  private mcpServer: Server | null = null;
  private mcpTransport: StdioServerTransport | null = null;

  /**
   * 创建 MCP 服务器实例
   */
  async createMCPServer(config: any = {}): Promise<Server> {
    this.mcpServer = new Server(
      {
        name: 'nocobase-mcp-test-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
          resources: {},
        },
      }
    );

    // 注册 MCP 工具
    await this.registerMCPTools();

    return this.mcpServer;
  }

  /**
   * 注册 MCP 工具
   */
  private async registerMCPTools(): Promise<void> {
    if (!this.mcpServer) return;

    // 注册工具列表处理器
    this.mcpServer.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'create_collection',
            description: 'Create a new collection in NocoBase',
            inputSchema: {
              type: 'object',
              properties: {
                name: {
                  type: 'string',
                  description: 'Collection name',
                },
                fields: {
                  type: 'array',
                  description: 'Collection fields configuration',
                },
              },
              required: ['name'],
            },
          },
          {
            name: 'query_records',
            description: 'Query records from a collection',
            inputSchema: {
              type: 'object',
              properties: {
                collection: {
                  type: 'string',
                  description: 'Collection name',
                },
                filter: {
                  type: 'object',
                  description: 'Query filter',
                },
                page: {
                  type: 'number',
                  description: 'Page number',
                },
                pageSize: {
                  type: 'number',
                  description: 'Page size',
                },
              },
              required: ['collection'],
            },
          },
          {
            name: 'create_record',
            description: 'Create a new record in a collection',
            inputSchema: {
              type: 'object',
              properties: {
                collection: {
                  type: 'string',
                  description: 'Collection name',
                },
                data: {
                  type: 'object',
                  description: 'Record data',
                },
              },
              required: ['collection', 'data'],
            },
          },
          {
            name: 'update_record',
            description: 'Update an existing record',
            inputSchema: {
              type: 'object',
              properties: {
                collection: {
                  type: 'string',
                  description: 'Collection name',
                },
                id: {
                  type: 'string',
                  description: 'Record ID',
                },
                data: {
                  type: 'object',
                  description: 'Update data',
                },
              },
              required: ['collection', 'id', 'data'],
            },
          },
          {
            name: 'delete_record',
            description: 'Delete a record',
            inputSchema: {
              type: 'object',
              properties: {
                collection: {
                  type: 'string',
                  description: 'Collection name',
                },
                id: {
                  type: 'string',
                  description: 'Record ID',
                },
              },
              required: ['collection', 'id'],
            },
          },
        ],
      };
    });

    // 注册工具调用处理器
    this.mcpServer.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'create_collection':
            return await this.createCollection(args);
          case 'query_records':
            return await this.queryRecords(args);
          case 'create_record':
            return await this.createRecord(args);
          case 'update_record':
            return await this.updateRecord(args);
          case 'delete_record':
            return await this.deleteRecord(args);
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  /**
   * 创建集合
   */
  private async createCollection(args: any) {
    const { name, fields = [] } = args;
    
    // 检查集合是否已存在
    const existingCollection = this.db.getCollection(name);
    if (existingCollection) {
      return {
        content: [
          {
            type: 'text',
            text: `Collection '${name}' already exists`,
          },
        ],
      };
    }

    // 创建新集合
    const collection = this.db.collection({
      name,
      fields: [
        {
          type: 'string',
          name: 'id',
          primaryKey: true,
        },
        ...fields,
      ],
    });

    await this.db.sync();

    return {
      content: [
        {
          type: 'text',
          text: `Collection '${name}' created successfully`,
        },
      ],
    };
  }

  /**
   * 查询记录
   */
  private async queryRecords(args: any) {
    const { collection, filter = {}, page = 1, pageSize = 20 } = args;
    
    const collectionInstance = this.db.getCollection(collection);
    if (!collectionInstance) {
      throw new Error(`Collection '${collection}' not found`);
    }

    const records = await collectionInstance.repository.find({
      filter,
      page,
      pageSize,
    });

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(records, null, 2),
        },
      ],
    };
  }

  /**
   * 创建记录
   */
  private async createRecord(args: any) {
    const { collection, data } = args;
    
    const collectionInstance = this.db.getCollection(collection);
    if (!collectionInstance) {
      throw new Error(`Collection '${collection}' not found`);
    }

    const record = await collectionInstance.repository.create({
      values: data,
    });

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(record, null, 2),
        },
      ],
    };
  }

  /**
   * 更新记录
   */
  private async updateRecord(args: any) {
    const { collection, id, data } = args;
    
    const collectionInstance = this.db.getCollection(collection);
    if (!collectionInstance) {
      throw new Error(`Collection '${collection}' not found`);
    }

    const record = await collectionInstance.repository.update({
      filterByTk: id,
      values: data,
    });

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(record, null, 2),
        },
      ],
    };
  }

  /**
   * 删除记录
   */
  private async deleteRecord(args: any) {
    const { collection, id } = args;
    
    const collectionInstance = this.db.getCollection(collection);
    if (!collectionInstance) {
      throw new Error(`Collection '${collection}' not found`);
    }

    await collectionInstance.repository.destroy({
      filterByTk: id,
    });

    return {
      content: [
        {
          type: 'text',
          text: `Record '${id}' deleted successfully`,
        },
      ],
    };
  }

  /**
   * 启动 MCP 服务器
   */
  async startMCPServer(): Promise<void> {
    if (!this.mcpServer) {
      throw new Error('MCP server not created');
    }

    this.mcpTransport = new StdioServerTransport();
    await this.mcpServer.connect(this.mcpTransport);
  }

  /**
   * 停止 MCP 服务器
   */
  async stopMCPServer(): Promise<void> {
    if (this.mcpServer) {
      await this.mcpServer.close();
      this.mcpServer = null;
    }
    if (this.mcpTransport) {
      this.mcpTransport = null;
    }
  }

  /**
   * 获取 MCP 服务器实例
   */
  getMCPServer(): Server | null {
    return this.mcpServer;
  }
}

/**
 * 创建 MCP 测试服务器
 */
export async function createMCPServer(options: any = {}): Promise<MCPPMockServer> {
  const app = await createMockServer(options);
  return app as MCPPMockServer;
}

/**
 * MCP 测试工具类
 */
export class MCPTestUtils {
  /**
   * 创建测试用的集合
   */
  static async createTestCollection(app: MCPPMockServer, name: string, fields: any[] = []) {
    return await app.agent().resource('collections').create({
      values: {
        name,
        fields: [
          {
            type: 'string',
            name: 'id',
            primaryKey: true,
          },
          {
            type: 'string',
            name: 'name',
          },
          ...fields,
        ],
      },
    });
  }

  /**
   * 创建测试用户
   */
  static async createTestUser(app: MCPPMockServer, userData: any = {}) {
    return await app.agent().resource('users').create({
      values: {
        email: `test${Date.now()}@example.com`,
        password: 'password123',
        ...userData,
      },
    });
  }

  /**
   * 清理测试数据
   */
  static async cleanupTestData(app: MCPPMockServer) {
    await app.cleanDb();
  }

  /**
   * 模拟 MCP 工具调用
   */
  static async simulateMCPToolCall(app: MCPPMockServer, toolName: string, args: any) {
    const mcpServer = app.getMCPServer();
    if (!mcpServer) {
      throw new Error('MCP server not available');
    }

    // 模拟工具调用
    const request = {
      method: 'tools/call',
      params: {
        name: toolName,
        arguments: args,
      },
    };

    // 这里需要根据实际的 MCP 协议实现调用逻辑
    // 由于是测试环境，我们可以直接调用内部方法
    switch (toolName) {
      case 'create_collection':
        return await app['createCollection'](args);
      case 'query_records':
        return await app['queryRecords'](args);
      case 'create_record':
        return await app['createRecord'](args);
      case 'update_record':
        return await app['updateRecord'](args);
      case 'delete_record':
        return await app['deleteRecord'](args);
      default:
        throw new Error(`Unknown tool: ${toolName}`);
    }
  }
}