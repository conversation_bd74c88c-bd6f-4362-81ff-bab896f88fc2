#!/usr/bin/env node

/**
 * MCP 测试启动脚本
 * 用于启动 MCP 测试服务器
 */

const { createMCPServer } = require('./dist/mcp/mcp-server');

async function main() {
  console.log('Starting MCP Test Server...');
  
  try {
    const app = await createMCPServer({
      database: {
        dialect: 'sqlite',
        storage: ':memory:',
      },
    });

    await app.loadAndInstall();
    await app.createMCPServer();
    await app.startMCPServer();

    console.log('MCP Test Server started successfully');
    console.log('Server is ready to accept MCP connections');

    // 优雅关闭处理
    process.on('SIGINT', async () => {
      console.log('Shutting down MCP Test Server...');
      await app.stopMCPServer();
      await app.destroy();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      console.log('Shutting down MCP Test Server...');
      await app.stopMCPServer();
      await app.destroy();
      process.exit(0);
    });

  } catch (error) {
    console.error('Failed to start MCP Test Server:', error);
    process.exit(1);
  }
}

main();