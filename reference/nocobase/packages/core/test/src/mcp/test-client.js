#!/usr/bin/env node

/**
 * MCP 测试客户端示例
 * 用于测试 MCP 服务器功能
 */

const { StdioClientTransport } = require('@modelcontextprotocol/sdk/client/stdio.js');
const { Client } = require('@modelcontextprotocol/sdk/client/index.js');

async function main() {
  console.log('Starting MCP Test Client...');

  // 创建传输层
  const transport = new StdioClientTransport({
    command: 'node',
    args: ['packages/core/test/src/mcp/start-test-server.js'],
  });

  // 创建客户端
  const client = new Client(
    {
      name: 'mcp-test-client',
      version: '1.0.0',
    },
    {
      capabilities: {},
    }
  );

  try {
    // 连接到服务器
    await client.connect(transport);
    console.log('Connected to MCP Test Server');

    // 测试工具列表
    console.log('\n=== Testing Tools List ===');
    const toolsResponse = await client.listTools();
    console.log('Available tools:', toolsResponse.tools.map(t => t.name));

    // 测试创建集合
    console.log('\n=== Testing Collection Creation ===');
    const createCollectionResult = await client.callTool({
      name: 'create_collection',
      arguments: {
        name: 'test_posts',
        fields: [
          {
            type: 'string',
            name: 'title',
          },
          {
            type: 'text',
            name: 'content',
          },
        ],
      },
    });
    console.log('Create collection result:', createCollectionResult.content[0].text);

    // 测试创建记录
    console.log('\n=== Testing Record Creation ===');
    const createRecordResult = await client.callTool({
      name: 'create_record',
      arguments: {
        collection: 'test_posts',
        data: {
          title: 'Hello MCP',
          content: 'This is a test post created via MCP',
        },
      },
    });
    console.log('Create record result:', createRecordResult.content[0].text);

    // 测试查询记录
    console.log('\n=== Testing Record Query ===');
    const queryResult = await client.callTool({
      name: 'query_records',
      arguments: {
        collection: 'test_posts',
      },
    });
    console.log('Query result:', queryResult.content[0].text);

    // 测试更新记录
    console.log('\n=== Testing Record Update ===');
    const record = JSON.parse(createRecordResult.content[0].text);
    const updateResult = await client.callTool({
      name: 'update_record',
      arguments: {
        collection: 'test_posts',
        id: record.id,
        data: {
          title: 'Updated Hello MCP',
          content: 'This post has been updated via MCP',
        },
      },
    });
    console.log('Update result:', updateResult.content[0].text);

    // 测试删除记录
    console.log('\n=== Testing Record Deletion ===');
    const deleteResult = await client.callTool({
      name: 'delete_record',
      arguments: {
        collection: 'test_posts',
        id: record.id,
      },
    });
    console.log('Delete result:', deleteResult.content[0].text);

    console.log('\n=== All Tests Completed Successfully ===');

  } catch (error) {
    console.error('Test failed:', error);
    process.exit(1);
  } finally {
    // 清理连接
    await client.close();
    process.exit(0);
  }
}

main().catch(console.error);