import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { createMCPServer, MCPTestUtils } from './mcp-server';
import { MCPPMockServer } from './mcp-server';

describe('MCP Integration Tests', () => {
  let app: MCPPMockServer;

  beforeEach(async () => {
    app = await createMCPServer({
      database: {
        dialect: 'sqlite',
        storage: ':memory:',
      },
    });
    await app.loadAndInstall();
    await app.createMCPServer();
  });

  afterEach(async () => {
    if (app) {
      await app.destroy();
    }
  });

  describe('NocoBase Integration', () => {
    it('should work with NocoBase plugin system', async () => {
      // 测试插件加载
      await app.load('test-plugin');
      expect(app.pm.get('test-plugin')).toBeDefined();
    });

    it('should work with NocoBase ACL system', async () => {
      // 创建测试用户
      const user = await MCPTestUtils.createTestUser(app, {
        email: '<EMAIL>',
        password: 'password123',
      });

      // 测试用户权限
      const agent = app.agent().loginUsingId(user.id);
      const result = await agent.resource('users').get();
      expect(result.status).toBe(200);
    });

    it('should work with NocoBase collections', async () => {
      // 创建 NocoBase 风格的集合
      const collection = await app.db.collection({
        name: 'test_collection',
        fields: [
          {
            type: 'string',
            name: 'title',
          },
          {
            type: 'text',
            name: 'content',
          },
          {
            type: 'belongsTo',
            name: 'user',
            target: 'users',
          },
        ],
      });

      await app.db.sync();

      // 验证集合创建成功
      expect(app.db.getCollection('test_collection')).toBeDefined();
    });
  });

  describe('MCP Protocol Compliance', () => {
    it('should follow MCP request/response format', async () => {
      // 模拟 MCP 请求
      const request = {
        jsonrpc: '2.0',
        id: 1,
        method: 'tools/call',
        params: {
          name: 'create_collection',
          arguments: {
            name: 'test_collection',
            fields: [
              {
                type: 'string',
                name: 'title',
              },
            ],
          },
        },
      };

      // 处理请求
      const result = await MCPTestUtils.simulateMCPToolCall(app, 'create_collection', {
        name: 'test_collection',
        fields: [
          {
            type: 'string',
            name: 'title',
          },
        ],
      });

      // 验证响应格式
      expect(result).toHaveProperty('content');
      expect(Array.isArray(result.content)).toBe(true);
      expect(result.content[0]).toHaveProperty('type');
      expect(result.content[0]).toHaveProperty('text');
    });

    it('should handle MCP errors properly', async () => {
      try {
        await MCPTestUtils.simulateMCPToolCall(app, 'invalid_tool', {});
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect(error.message).toContain('Unknown tool');
      }
    });
  });

  describe('Real-world Scenarios', () => {
    it('should handle complex data models', async () => {
      // 创建用户集合
      await MCPTestUtils.simulateMCPToolCall(app, 'create_collection', {
        name: 'users',
        fields: [
          {
            type: 'string',
            name: 'name',
          },
          {
            type: 'string',
            name: 'email',
          },
        ],
      });

      // 创建文章集合
      await MCPTestUtils.simulateMCPToolCall(app, 'create_collection', {
        name: 'posts',
        fields: [
          {
            type: 'string',
            name: 'title',
          },
          {
            type: 'text',
            name: 'content',
          },
          {
            type: 'belongsTo',
            name: 'author',
            target: 'users',
          },
        ],
      });

      // 创建评论集合
      await MCPTestUtils.simulateMCPToolCall(app, 'create_collection', {
        name: 'comments',
        fields: [
          {
            type: 'text',
            name: 'content',
          },
          {
            type: 'belongsTo',
            name: 'post',
            target: 'posts',
          },
          {
            type: 'belongsTo',
            name: 'user',
            target: 'users',
          },
        ],
      });

      // 创建测试数据
      const user = await MCPTestUtils.simulateMCPToolCall(app, 'create_record', {
        collection: 'users',
        data: {
          name: 'John Doe',
          email: '<EMAIL>',
        },
      });

      const post = await MCPTestUtils.simulateMCPToolCall(app, 'create_record', {
        collection: 'posts',
        data: {
          title: 'Test Post',
          content: 'This is a test post',
          author: JSON.parse(user.content[0].text).id,
        },
      });

      const comment = await MCPTestUtils.simulateMCPToolCall(app, 'create_record', {
        collection: 'comments',
        data: {
          content: 'Great post!',
          post: JSON.parse(post.content[0].text).id,
          user: JSON.parse(user.content[0].text).id,
        },
      });

      // 验证数据完整性
      const posts = await MCPTestUtils.simulateMCPToolCall(app, 'query_records', {
        collection: 'posts',
        filter: {
          'author.name': 'John Doe',
        },
      });

      expect(JSON.parse(posts.content[0].text).length).toBe(1);
    });

    it('should handle concurrent operations', async () => {
      // 创建测试集合
      await MCPTestUtils.simulateMCPToolCall(app, 'create_collection', {
        name: 'concurrent_test',
        fields: [
          {
            type: 'string',
            name: 'name',
          },
          {
            type: 'integer',
            name: 'counter',
          },
        ],
      });

      // 并发创建记录
      const promises = [];
      for (let i = 0; i < 10; i++) {
        promises.push(
          MCPTestUtils.simulateMCPToolCall(app, 'create_record', {
            collection: 'concurrent_test',
            data: {
              name: `Record ${i}`,
              counter: i,
            },
          })
        );
      }

      const results = await Promise.all(promises);
      
      // 验证所有记录都创建成功
      const queryResult = await MCPTestUtils.simulateMCPToolCall(app, 'query_records', {
        collection: 'concurrent_test',
      });

      const records = JSON.parse(queryResult.content[0].text);
      expect(records.length).toBe(10);
    });
  });

  describe('Security and Validation', () => {
    it('should validate input data', async () => {
      // 创建带验证规则的集合
      await MCPTestUtils.simulateMCPToolCall(app, 'create_collection', {
        name: 'validated_collection',
        fields: [
          {
            type: 'string',
            name: 'email',
            pattern: '^[^@]+@[^@]+\\.[^@]+$',
          },
          {
            type: 'integer',
            name: 'age',
            minimum: 0,
          },
        ],
      });

      // 测试有效数据
      const validResult = await MCPTestUtils.simulateMCPToolCall(app, 'create_record', {
        collection: 'validated_collection',
        data: {
          email: '<EMAIL>',
          age: 25,
        },
      });

      expect(validResult.isError).toBeUndefined();

      // 测试无效数据
      const invalidResult = await MCPTestUtils.simulateMCPToolCall(app, 'create_record', {
        collection: 'validated_collection',
        data: {
          email: 'invalid-email',
          age: -1,
        },
      });

      // 注意：这里的具体错误处理取决于你的验证实现
      expect(invalidResult).toBeDefined();
    });

    it('should handle permissions correctly', async () => {
      // 创建测试用户
      const user = await MCPTestUtils.createTestUser(app);
      
      // 创建受限集合
      await MCPTestUtils.simulateMCPToolCall(app, 'create_collection', {
        name: 'restricted_collection',
        fields: [
          {
            type: 'string',
            name: 'secret',
          },
        ],
      });

      // 使用普通用户权限进行操作
      const agent = app.agent().loginUsingId(user.id);
      
      // 这里应该测试权限控制逻辑
      // 具体实现取决于你的权限系统
    });
  });
});