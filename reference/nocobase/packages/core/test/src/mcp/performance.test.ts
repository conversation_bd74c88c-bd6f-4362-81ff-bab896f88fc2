import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { createMCPServer } from './mcp-server';
import { MCPPMockServer } from './mcp-server';

describe('MCP Performance Tests', () => {
  let app: MCPPMockServer;

  beforeEach(async () => {
    app = await createMCPServer({
      database: {
        dialect: 'sqlite',
        storage: ':memory:',
      },
    });
    await app.loadAndInstall();
    await app.createMCPServer();
  });

  afterEach(async () => {
    if (app) {
      await app.destroy();
    }
  });

  describe('Collection Creation Performance', () => {
    it('should create collections quickly', async () => {
      const startTime = Date.now();
      const collectionCount = 50;

      // 批量创建集合
      for (let i = 0; i < collectionCount; i++) {
        await app['createCollection']({
          name: `perf_collection_${i}`,
          fields: [
            {
              type: 'string',
              name: 'name',
            },
            {
              type: 'text',
              name: 'description',
            },
          ],
        });
      }

      const duration = Date.now() - startTime;
      console.log(`Created ${collectionCount} collections in ${duration}ms`);
      
      expect(duration).toBeLessThan(10000); // 10秒内完成50个集合创建
    });

    it('should handle large field configurations', async () => {
      const fieldCount = 100;
      const fields = [];

      for (let i = 0; i < fieldCount; i++) {
        fields.push({
          type: 'string',
          name: `field_${i}`,
        });
      }

      const startTime = Date.now();
      await app['createCollection']({
        name: 'large_field_collection',
        fields,
      });

      const duration = Date.now() - startTime;
      console.log(`Created collection with ${fieldCount} fields in ${duration}ms`);
      
      expect(duration).toBeLessThan(5000); // 5秒内完成
    });
  });

  describe('Record Operations Performance', () => {
    beforeEach(async () => {
      // 创建测试集合
      await app['createCollection']({
        name: 'perf_records',
        fields: [
          {
            type: 'string',
            name: 'title',
          },
          {
            type: 'text',
            name: 'content',
          },
          {
            type: 'integer',
            name: 'priority',
          },
        ],
      });
    });

    it('should handle bulk record creation', async () => {
      const recordCount = 1000;
      const startTime = Date.now();

      // 批量创建记录
      const promises = [];
      for (let i = 0; i < recordCount; i++) {
        promises.push(
          app['createRecord']({
            collection: 'perf_records',
            data: {
              title: `Record ${i}`,
              content: `Content for record ${i}`,
              priority: i % 10,
            },
          })
        );
      }

      await Promise.all(promises);
      const createDuration = Date.now() - startTime;

      // 验证记录数量
      const result = await app['queryRecords']({
        collection: 'perf_records',
      });
      const records = JSON.parse(result.content[0].text);
      
      expect(records.length).toBe(recordCount);
      console.log(`Created ${recordCount} records in ${createDuration}ms`);
      expect(createDuration).toBeLessThan(30000); // 30秒内完成1000条记录创建
    });

    it('should handle complex queries efficiently', async () => {
      // 创建测试数据
      const recordCount = 500;
      for (let i = 0; i < recordCount; i++) {
        await app['createRecord']({
          collection: 'perf_records',
          data: {
            title: `Record ${i}`,
            content: `Content for record ${i}`,
            priority: i % 10,
          },
        });
      }

      // 测试查询性能
      const queryStartTime = Date.now();
      const result = await app['queryRecords']({
        collection: 'perf_records',
        filter: {
          priority: {
            $gte: 5,
          },
        },
        page: 1,
        pageSize: 100,
      });

      const queryDuration = Date.now() - queryStartTime;
      const records = JSON.parse(result.content[0].text);
      
      console.log(`Complex query completed in ${queryDuration}ms, returned ${records.length} records`);
      expect(queryDuration).toBeLessThan(1000); // 1秒内完成查询
    });

    it('should handle concurrent operations', async () => {
      const concurrentUsers = 20;
      const operationsPerUser = 10;
      
      const startTime = Date.now();
      
      // 模拟并发用户操作
      const userPromises = [];
      for (let user = 0; user < concurrentUsers; user++) {
        const userOperations = [];
        for (let op = 0; op < operationsPerUser; op++) {
          userOperations.push(
            app['createRecord']({
              collection: 'perf_records',
              data: {
                title: `User ${user} - Operation ${op}`,
                content: `Content by user ${user}`,
                priority: Math.floor(Math.random() * 10),
              },
            })
          );
        }
        userPromises.push(Promise.all(userOperations));
      }

      await Promise.all(userPromises);
      const duration = Date.now() - startTime;
      
      const totalOperations = concurrentUsers * operationsPerUser;
      console.log(`Completed ${totalOperations} concurrent operations in ${duration}ms`);
      expect(duration).toBeLessThan(15000); // 15秒内完成200个并发操作
    });
  });

  describe('Memory Usage', () => {
    it('should manage memory efficiently during large operations', async () => {
      // 创建测试集合
      await app['createCollection']({
        name: 'memory_test',
        fields: [
          {
            type: 'string',
            name: 'title',
          },
          {
            type: 'text',
            name: 'large_content',
          },
        ],
      });

      // 生成大量数据
      const largeContent = 'x'.repeat(10000); // 10KB内容
      const recordCount = 100;
      
      const startMemory = process.memoryUsage();
      
      for (let i = 0; i < recordCount; i++) {
        await app['createRecord']({
          collection: 'memory_test',
          data: {
            title: `Large Record ${i}`,
            large_content: largeContent,
          },
        });
      }

      const endMemory = process.memoryUsage();
      const memoryIncrease = endMemory.heapUsed - startMemory.heapUsed;
      
      console.log(`Memory increase for ${recordCount} large records: ${Math.round(memoryIncrease / 1024 / 1024)}MB`);
      
      // 内存增长应该在合理范围内
      expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024); // 小于100MB
    });
  });

  describe('Throughput Tests', () => {
    it('should handle sustained load', async () => {
      // 创建测试集合
      await app['createCollection']({
        name: 'throughput_test',
        fields: [
          {
            type: 'string',
            name: 'operation',
          },
          {
            type: 'datetime',
            name: 'timestamp',
          },
        ],
      });

      const testDuration = 10000; // 10秒
      const startTime = Date.now();
      let operationCount = 0;

      // 持续生成负载
      const loadGenerator = async () => {
        while (Date.now() - startTime < testDuration) {
          await app['createRecord']({
            collection: 'throughput_test',
            data: {
              operation: 'write',
              timestamp: new Date().toISOString(),
            },
          });
          operationCount++;
        }
      };

      // 启动多个负载生成器
      const generators = [];
      const generatorCount = 5;
      for (let i = 0; i < generatorCount; i++) {
        generators.push(loadGenerator());
      }

      await Promise.all(generators);
      
      const throughput = operationCount / (testDuration / 1000);
      console.log(`Throughput: ${throughput.toFixed(2)} operations/second`);
      
      expect(throughput).toBeGreaterThan(10); // 每秒至少10个操作
    });
  });
});