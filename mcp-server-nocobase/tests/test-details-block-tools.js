/**
 * 测试详情区块工具功能
 * 验证新增的详情区块相关工具能力
 */

import { NocoBaseClient } from './dist/client.js';

// 配置信息
const config = {
  baseUrl: 'https://app.dev.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
  app: 'mcp_playground'
};

async function testDetailsBlockTools() {
  const client = new NocoBaseClient(config);
  
  console.log('🧪 开始测试详情区块工具功能...\n');

  try {
    // 1. 测试获取详情数据
    console.log('1️⃣ 测试获取详情数据...');
    try {
      // 首先获取一个用户记录用于测试
      const users = await client.listRecords('users', { pageSize: 1 });
      if (users.data.length > 0) {
        const userId = users.data[0].id;
        console.log(`   使用用户ID: ${userId}`);
        
        const detailsData = await client.getDetailsData('users', userId, {
          fields: ['id', 'nickname', 'username', 'email'],
          appends: ['roles']
        });
        console.log('   ✅ 获取详情数据成功');
        console.log('   📄 数据:', JSON.stringify(detailsData, null, 2));
      } else {
        console.log('   ⚠️  没有找到用户记录，跳过详情数据测试');
      }
    } catch (error) {
      console.log('   ❌ 获取详情数据失败:', error.message);
    }

    // 2. 测试导出详情数据
    console.log('\n2️⃣ 测试导出详情数据...');
    try {
      const exportResult = await client.exportDetailsData('users', {
        fields: ['id', 'nickname', 'username', 'email'],
        format: 'xlsx'
      });
      console.log('   ✅ 导出详情数据成功');
      console.log('   📄 导出结果:', typeof exportResult);
    } catch (error) {
      console.log('   ❌ 导出详情数据失败:', error.message);
    }

    // 3. 测试自定义详情请求
    console.log('\n3️⃣ 测试自定义详情请求...');
    try {
      const customResult = await client.customDetailsRequest({
        url: '/users:list',
        method: 'GET',
        params: { pageSize: 1 }
      });
      console.log('   ✅ 自定义详情请求成功');
      console.log('   📄 请求结果:', customResult.data ? '有数据' : '无数据');
    } catch (error) {
      console.log('   ❌ 自定义详情请求失败:', error.message);
    }

    // 4. 测试创建增强的详情区块
    console.log('\n4️⃣ 测试创建增强的详情区块...');
    try {
      // 获取一个页面的 Grid UID 用于测试
      const routes = await client.listRoutes();
      const testRoute = routes.find(r => r.type === 'page' && r.schemaUid);
      
      if (testRoute) {
        console.log(`   使用页面: ${testRoute.title} (${testRoute.schemaUid})`);
        
        const pageSchema = await client.getSchemaProperties(testRoute.schemaUid);
        const gridUid = findGridUid(pageSchema);
        
        if (gridUid) {
          console.log(`   找到Grid UID: ${gridUid}`);
          
          // 创建带自定义字段的详情区块
          const { BLOCK_TEMPLATES } = await import('./dist/block-templates.js');
          const detailsTemplate = BLOCK_TEMPLATES.details;
          
          const blockSchema = detailsTemplate.createSchema({
            collectionName: 'users',
            title: '用户详情测试',
            includeActions: true,
            includePagination: false,
            customFields: [
              { name: 'nickname', title: '昵称', component: 'Input.ReadPretty', span: 12 },
              { name: 'username', title: '用户名', component: 'Input.ReadPretty', span: 12 },
              { name: 'email', title: '邮箱', component: 'Input.ReadPretty', span: 24 }
            ]
          });
          
          console.log('   ✅ 创建增强详情区块Schema成功');
          console.log('   📄 区块UID:', blockSchema['x-uid']);
          
          // 注意：这里不实际插入到页面，只是测试Schema生成
          console.log('   ℹ️  Schema生成成功，未实际插入页面');
          
        } else {
          console.log('   ⚠️  未找到Grid容器，跳过区块创建测试');
        }
      } else {
        console.log('   ⚠️  未找到测试页面，跳过区块创建测试');
      }
    } catch (error) {
      console.log('   ❌ 创建增强详情区块失败:', error.message);
    }

    // 5. 测试关联详情区块Schema生成
    console.log('\n5️⃣ 测试关联详情区块Schema生成...');
    try {
      const { BLOCK_TEMPLATES } = await import('./dist/block-templates.js');
      const detailsTemplate = BLOCK_TEMPLATES.details;
      
      const associationBlockSchema = detailsTemplate.createSchema({
        collectionName: 'roles',
        association: 'users.roles',
        title: '用户角色详情',
        includeActions: true,
        customFields: [
          { name: 'name', title: '角色名称', component: 'Input.ReadPretty' },
          { name: 'title', title: '角色标题', component: 'Input.ReadPretty' }
        ]
      });
      
      console.log('   ✅ 关联详情区块Schema生成成功');
      console.log('   📄 关联:', associationBlockSchema['x-decorator-props'].association);
      console.log('   📄 资源:', associationBlockSchema['x-decorator-props'].resource);
      
    } catch (error) {
      console.log('   ❌ 关联详情区块Schema生成失败:', error.message);
    }

    console.log('\n🎉 详情区块工具测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 辅助函数：查找Grid UID
function findGridUid(schema, path = '') {
  if (!schema || typeof schema !== 'object') return null;
  
  if (schema['x-component'] === 'Grid') {
    return schema['x-uid'];
  }
  
  if (schema.properties) {
    for (const [key, value] of Object.entries(schema.properties)) {
      const result = findGridUid(value, path ? `${path}.${key}` : key);
      if (result) return result;
    }
  }
  
  return null;
}

// 运行测试
testDetailsBlockTools().catch(console.error);

export { testDetailsBlockTools };
