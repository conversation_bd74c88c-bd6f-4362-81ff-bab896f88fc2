#!/usr/bin/env node

// 测试新的命名验证功能
import { NocoBaseClient } from './dist/client.js';

async function testNamingValidation() {
  console.log('🧪 测试命名验证功能...\n');

  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  // 测试用例：好的命名
  console.log('✅ 测试1: 符合规范的命名');
  try {
    await client.deleteCollection('test_products');
  } catch (error) {
    // 忽略删除错误
  }

  try {
    const goodCollection = await client.createCollectionWithDefaults({
      name: 'test_products',
      title: 'Test Products',
      description: 'Product catalog for testing naming standards',
      category: ['Commerce', 'Testing'],
      autoGenId: true,
      createdAt: true,
      updatedAt: true,
      fields: [
        {
          name: 'name',
          type: 'string',
          interface: 'input',
          uiSchema: {
            type: 'string',
            title: 'Product Name',
            'x-component': 'Input',
            required: true
          }
        }
      ]
    });
    console.log(`✅ 成功创建符合规范的集合: ${goodCollection.name}`);
  } catch (error) {
    console.log(`❌ 创建失败: ${error.message}`);
  }

  console.log('\n❌ 测试2: 不符合规范的命名（单数形式）');
  try {
    await client.deleteCollection('test_product');
  } catch (error) {
    // 忽略删除错误
  }

  try {
    const singularCollection = await client.createCollectionWithDefaults({
      name: 'test_product', // 单数形式
      title: 'Test Product',
      description: 'Single product for testing',
      fields: [
        {
          name: 'name',
          type: 'string',
          interface: 'input',
          uiSchema: {
            type: 'string',
            title: 'Product Name',
            'x-component': 'Input'
          }
        }
      ]
    });
    console.log(`⚠️  创建了单数形式的集合: ${singularCollection.name} (建议使用复数形式)`);
  } catch (error) {
    console.log(`❌ 创建失败: ${error.message}`);
  }

  console.log('\n❌ 测试3: 中文标题和描述');
  try {
    await client.deleteCollection('test_orders');
  } catch (error) {
    // 忽略删除错误
  }

  try {
    const chineseCollection = await client.createCollectionWithDefaults({
      name: 'test_orders',
      title: '测试订单', // 中文标题
      description: '用于测试的订单集合', // 中文描述
      fields: [
        {
          name: 'order_number',
          type: 'string',
          interface: 'input',
          uiSchema: {
            type: 'string',
            title: 'Order Number',
            'x-component': 'Input'
          }
        }
      ]
    });
    console.log(`⚠️  创建了包含中文的集合: ${chineseCollection.name} (建议使用英文)`);
  } catch (error) {
    console.log(`❌ 创建失败: ${error.message}`);
  }

  console.log('\n🔍 验证所有测试集合:');
  try {
    const collections = await client.listCollections();
    const testCollections = collections.filter(c => c.name.startsWith('test_'));
    
    console.log(`找到 ${testCollections.length} 个测试集合:`);
    testCollections.forEach(collection => {
      console.log(`   • ${collection.name} - ${collection.title}`);
    });

    // 清理测试集合
    console.log('\n🧹 清理测试集合:');
    for (const collection of testCollections) {
      try {
        await client.deleteCollection(collection.name);
        console.log(`✅ 删除测试集合: ${collection.name}`);
      } catch (error) {
        console.log(`⚠️  删除失败: ${collection.name} - ${error.message}`);
      }
    }

  } catch (error) {
    console.log(`❌ 验证失败: ${error.message}`);
  }

  console.log('\n🎉 命名验证测试完成！');
  console.log('\n📊 测试总结:');
  console.log('   ✅ 符合规范的命名可以正常创建');
  console.log('   ⚠️  不符合规范的命名会收到警告但仍可创建');
  console.log('   💡 系统会提供命名建议和最佳实践提示');
}

testNamingValidation().catch(console.error);
