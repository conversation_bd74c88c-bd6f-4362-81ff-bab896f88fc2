#!/usr/bin/env node

// 测试使用MCP工具正确创建集合
import { NocoBaseClient } from './dist/client.js';

async function testMCPCreateCollection() {
  console.log('🧪 测试使用MCP工具创建集合的正确方法...\n');

  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  try {
    // 模拟MCP工具中create_collection的逻辑
    console.log('📋 使用正确的方法创建测试集合');
    
    const params = {
      name: 'test_districts',
      title: '测试区县集合',
      description: '测试用区县信息集合',
      autoGenId: true,
      createdAt: true,
      updatedAt: true,
      createdBy: true,
      updatedBy: true,
      fields: [
        {
          name: 'province',
          type: 'string',
          interface: 'input',
          uiSchema: {
            type: 'string',
            title: '省份',
            'x-component': 'Input',
            required: true
          }
        },
        {
          name: 'city',
          type: 'string',
          interface: 'input',
          uiSchema: {
            type: 'string',
            title: '城市',
            'x-component': 'Input',
            required: true
          }
        },
        {
          name: 'district',
          type: 'string',
          interface: 'input',
          uiSchema: {
            type: 'string',
            title: '区县',
            'x-component': 'Input',
            required: true
          }
        }
      ]
    };

    // 准备默认字段定义（模拟MCP工具逻辑）
    const defaultFields = [];

    // ID 字段
    if (params.autoGenId !== false) {
      defaultFields.push({
        name: 'id',
        type: 'bigInt',
        interface: 'id',
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
        uiSchema: {
          type: 'number',
          title: '{{t("ID")}}',
          'x-component': 'InputNumber',
          'x-read-pretty': true
        }
      });
    }

    // 创建时间字段
    if (params.createdAt !== false) {
      defaultFields.push({
        name: 'createdAt',
        type: 'date',
        interface: 'createdAt',
        field: 'createdAt',
        uiSchema: {
          type: 'datetime',
          title: '{{t("Created at")}}',
          'x-component': 'DatePicker',
          'x-component-props': {},
          'x-read-pretty': true
        }
      });
    }

    // 更新时间字段
    if (params.updatedAt !== false) {
      defaultFields.push({
        name: 'updatedAt',
        type: 'date',
        interface: 'updatedAt',
        field: 'updatedAt',
        uiSchema: {
          type: 'datetime',
          title: '{{t("Last updated at")}}',
          'x-component': 'DatePicker',
          'x-component-props': {},
          'x-read-pretty': true
        }
      });
    }

    // 创建人字段
    if (params.createdBy === true) {
      defaultFields.push({
        name: 'createdBy',
        type: 'belongsTo',
        interface: 'createdBy',
        target: 'users',
        foreignKey: 'createdById',
        targetKey: 'id',
        uiSchema: {
          type: 'object',
          title: '{{t("Created by")}}',
          'x-component': 'AssociationField',
          'x-component-props': {
            fieldNames: {
              value: 'id',
              label: 'nickname'
            }
          },
          'x-read-pretty': true
        }
      });
    }

    // 更新人字段
    if (params.updatedBy === true) {
      defaultFields.push({
        name: 'updatedBy',
        type: 'belongsTo',
        interface: 'updatedBy',
        target: 'users',
        foreignKey: 'updatedById',
        targetKey: 'id',
        uiSchema: {
          type: 'object',
          title: '{{t("Last updated by")}}',
          'x-component': 'AssociationField',
          'x-component-props': {
            fieldNames: {
              value: 'id',
              label: 'nickname'
            }
          },
          'x-read-pretty': true
        }
      });
    }

    // 合并用户提供的字段和默认字段
    const allFields = [...defaultFields, ...(params.fields || [])];

    // 准备集合参数
    const collectionParams = {
      ...params,
      fields: allFields
    };

    // 删除已存在的测试集合（如果存在）
    try {
      await client.deleteCollection('test_districts');
      console.log('ℹ️  删除已存在的测试集合');
    } catch (error) {
      // 集合不存在，忽略错误
    }

    // 创建集合
    const collection = await client.createCollection(collectionParams);
    console.log(`✅ 成功创建测试集合: ${collection.name} (${collection.title})`);

    // 验证字段
    console.log('\n🔍 验证创建的字段:');
    const fields = await client.listFields('test_districts');
    console.log(`✅ 共有 ${fields.length} 个字段:`);
    fields.forEach(field => {
      console.log(`   • ${field.name} (${field.type}) - ${field.interface || 'no interface'} - ${field.uiSchema?.title || '无标题'}`);
    });

    // 创建测试数据
    console.log('\n📋 创建测试数据:');
    const record = await client.createRecord('test_districts', {
      province: '广东省',
      city: '广州市',
      district: '天河区'
    });
    console.log(`✅ 创建测试记录: ${record.province} ${record.city} ${record.district}`);

    // 检查记录的完整数据
    console.log('\n🔍 检查记录的完整数据:');
    const records = await client.listRecords('test_districts', { pageSize: 1 });
    if (records.data.length > 0) {
      const record = records.data[0];
      console.log('记录中的所有字段:');
      Object.keys(record).forEach(key => {
        console.log(`   ${key}: ${record[key]}`);
      });
    }

    console.log('\n🎉 测试完成！这就是正确创建集合的方法。');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

testMCPCreateCollection().catch(console.error);
