#!/usr/bin/env node

// 验证命名优化的最终结果
import { NocoBaseClient } from './dist/client.js';

async function verifyNamingOptimization() {
  console.log('🔍 验证命名优化结果...\n');

  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  try {
    // 1. 检查地理相关集合
    console.log('🌍 检查地理行政区划集合:');
    const geographicCollections = ['provinces', 'cities', 'districts', 'towns', 'villages'];
    
    for (const collectionName of geographicCollections) {
      try {
        const collection = await client.getCollection(collectionName);
        console.log(`✅ ${collectionName}:`);
        console.log(`   标题: ${collection.title}`);
        console.log(`   描述: ${collection.description || '无描述'}`);
        
        // 检查字段
        const fields = await client.listFields(collectionName);
        const customFields = fields.filter(f => !['id', 'createdAt', 'updatedAt', 'createdBy', 'updatedBy'].includes(f.name));
        console.log(`   自定义字段: ${customFields.map(f => f.name).join(', ')}`);
        console.log('');
      } catch (error) {
        console.log(`❌ ${collectionName}: ${error.message}`);
      }
    }

    // 2. 检查命名规范符合性
    console.log('📋 检查命名规范符合性:');
    const allCollections = await client.listCollections();
    
    const namingAnalysis = {
      pluralNames: 0,
      singularNames: 0,
      englishTitles: 0,
      chineseTitles: 0,
      englishDescriptions: 0,
      chineseDescriptions: 0,
      total: allCollections.length
    };

    allCollections.forEach(collection => {
      // 检查名称是否为复数
      if (collection.name.endsWith('s') || collection.name.includes('_')) {
        namingAnalysis.pluralNames++;
      } else {
        namingAnalysis.singularNames++;
      }

      // 检查标题语言
      if (collection.title && /[\u4e00-\u9fa5]/.test(collection.title)) {
        namingAnalysis.chineseTitles++;
      } else {
        namingAnalysis.englishTitles++;
      }

      // 检查描述语言
      if (collection.description) {
        if (/[\u4e00-\u9fa5]/.test(collection.description)) {
          namingAnalysis.chineseDescriptions++;
        } else {
          namingAnalysis.englishDescriptions++;
        }
      }
    });

    console.log(`总集合数: ${namingAnalysis.total}`);
    console.log(`复数名称: ${namingAnalysis.pluralNames} (${Math.round(namingAnalysis.pluralNames/namingAnalysis.total*100)}%)`);
    console.log(`单数名称: ${namingAnalysis.singularNames} (${Math.round(namingAnalysis.singularNames/namingAnalysis.total*100)}%)`);
    console.log(`英文标题: ${namingAnalysis.englishTitles} (${Math.round(namingAnalysis.englishTitles/namingAnalysis.total*100)}%)`);
    console.log(`中文标题: ${namingAnalysis.chineseTitles} (${Math.round(namingAnalysis.chineseTitles/namingAnalysis.total*100)}%)`);
    console.log('');

    // 3. 展示优化后的集合示例
    console.log('🎯 优化后的集合示例:');
    const optimizedCollections = ['towns', 'provinces', 'cities', 'districts', 'villages'];
    
    for (const name of optimizedCollections) {
      try {
        const collection = await client.getCollection(name);
        console.log(`📋 ${name}:`);
        console.log(`   Name: ${collection.name} (${collection.name.endsWith('s') ? '✅ 复数' : '⚠️  单数'})`);
        console.log(`   Title: ${collection.title} (${/[\u4e00-\u9fa5]/.test(collection.title || '') ? '⚠️  中文' : '✅ 英文'})`);
        console.log(`   Description: ${collection.description || 'N/A'} (${collection.description && /[\u4e00-\u9fa5]/.test(collection.description) ? '⚠️  中文' : '✅ 英文'})`);
        console.log('');
      } catch (error) {
        console.log(`❌ ${name}: 集合不存在`);
      }
    }

    // 4. 检查一些记录数据
    console.log('📊 检查数据记录:');
    try {
      const townsRecords = await client.listRecords('towns', { pageSize: 3 });
      console.log(`Towns集合记录数: ${townsRecords.data.length}`);
      if (townsRecords.data.length > 0) {
        console.log('示例记录:');
        townsRecords.data.forEach((record, index) => {
          console.log(`   ${index + 1}. ${record.city} ${record.district} ${record.name}`);
        });
      }
    } catch (error) {
      console.log(`❌ 无法获取Towns记录: ${error.message}`);
    }

    console.log('\n🎉 命名优化验证完成！');
    
    // 5. 生成优化报告
    console.log('\n📊 优化成果报告:');
    console.log('✅ 已完成的优化:');
    console.log('   • 更新了MCP工具的参数验证，增加了命名规范检查');
    console.log('   • 添加了命名验证函数，提供实时建议和警告');
    console.log('   • 更新了镇街集合，使用英文标题和描述');
    console.log('   • 创建了标准的地理行政区划集合体系');
    console.log('   • 建立了完整的命名规范文档');
    console.log('   • 增强了客户端方法，支持category参数');
    
    console.log('\n💡 命名规范要点:');
    console.log('   • Collection Name: 使用复数名词，小写，下划线分隔');
    console.log('   • Collection Title: 使用英文，首字母大写');
    console.log('   • Description: 使用英文，描述集合用途和功能');
    console.log('   • Category: 使用英文分类标签');
    
    console.log('\n🔧 工具增强:');
    console.log('   • MCP create_collection工具现在包含命名验证');
    console.log('   • 客户端createCollectionWithDefaults方法支持category');
    console.log('   • 自动提供命名建议和最佳实践提示');

  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error.message);
  }
}

verifyNamingOptimization().catch(console.error);
