#!/usr/bin/env node

// 验证镇街集合创建结果
import { NocoBaseClient } from './dist/client.js';

async function verifyTownsCollection() {
  console.log('🔍 验证镇街集合创建结果...\n');

  // 创建NocoBase客户端
  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  try {
    // 1. 检查集合是否存在
    console.log('📋 检查镇街集合');
    const collection = await client.getCollection('towns');
    console.log(`✅ 镇街集合存在: ${collection.name} (${collection.title})`);
    console.log(`   描述: ${collection.description || '无描述'}`);
    console.log();

    // 2. 检查字段
    console.log('📋 检查集合字段');
    const fields = await client.listFields('towns');
    console.log(`✅ 找到 ${fields.length} 个字段:`);
    fields.forEach(field => {
      console.log(`   • ${field.name} (${field.type}) - ${field.uiSchema?.title || '无标题'}`);
    });
    console.log();

    // 3. 检查数据记录
    console.log('📋 检查镇街数据记录');
    const records = await client.listRecords('towns');
    console.log(`✅ 找到 ${records.data.length} 条镇街记录:`);
    records.data.forEach(record => {
      console.log(`   • ID: ${record.id} - ${record.city} ${record.district} ${record.name}`);
    });
    console.log();

    // 4. 列出所有集合，确认镇街集合在列表中
    console.log('📋 检查所有集合列表');
    const allCollections = await client.listCollections();
    const townsInList = allCollections.find(c => c.name === 'towns');
    if (townsInList) {
      console.log(`✅ 镇街集合在集合列表中: ${townsInList.name} (${townsInList.title})`);
    } else {
      console.log('❌ 镇街集合不在集合列表中');
    }

    console.log();
    console.log('🎉 镇街集合验证完成！');

  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error.message);
  }
}

// 运行验证脚本
verifyTownsCollection().catch(console.error);
