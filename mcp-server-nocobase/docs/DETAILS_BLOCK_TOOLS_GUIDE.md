# NocoBase MCP 详情区块工具指南

本文档介绍了为 NocoBase MCP 服务器新增的详情区块（Details Block）相关工具能力，这些工具基于 [NocoBase Details Block API 映射文档](./block-api-mappings/nocobase-details-block-api-mapping.md) 实现。

## 🎯 新增工具概览

### 1. 详情数据操作工具

#### `get_details_data` - 获取详情数据
获取详情区块中单条记录的详细信息。

**参数：**
- `collectionName` (string, 必需): 集合名称
- `recordId` (string|number, 必需): 记录ID
- `fields` (array, 可选): 要获取的字段列表
- `appends` (array, 可选): 要关联的字段列表
- `except` (array, 可选): 要排除的字段列表

**示例：**
```javascript
await client.getDetailsData('users', 1, {
  fields: ['id', 'nickname', 'username', 'email'],
  appends: ['roles', 'profile']
});
```

#### `update_details_record` - 更新详情记录
更新详情区块中的记录数据。

**参数：**
- `collectionName` (string, 必需): 集合名称
- `recordId` (string|number, 必需): 记录ID
- `values` (object, 必需): 要更新的值
- `overwriteValues` (object, 可选): 覆盖值
- `assignedValues` (object, 可选): 分配值
- `updateAssociationValues` (boolean, 可选): 是否更新关联值
- `triggerWorkflows` (string, 可选): 触发的工作流

#### `delete_details_record` - 删除详情记录
删除详情区块中的记录。

**参数：**
- `collectionName` (string, 必需): 集合名称
- `recordId` (string|number, 必需): 记录ID
- `triggerWorkflows` (string, 可选): 触发的工作流

### 2. 详情数据导出和批量操作工具

#### `export_details_data` - 导出详情数据
导出详情区块的数据到文件。

**参数：**
- `collectionName` (string, 必需): 集合名称
- `recordId` (string|number, 可选): 特定记录ID
- `filter` (object, 可选): 过滤条件
- `fields` (array, 可选): 导出字段
- `format` (string, 可选): 导出格式 ('csv' | 'xlsx')

#### `bulk_update_details_records` - 批量更新详情记录
批量更新多条详情记录。

**参数：**
- `collectionName` (string, 必需): 集合名称
- `values` (object, 必需): 更新值
- `filter` (object, 可选): 过滤条件
- `forceUpdate` (boolean, 可选): 强制更新

### 3. 详情区块配置工具

#### `configure_details_fields` - 配置详情字段
配置详情区块中显示的字段。

**参数：**
- `blockUid` (string, 必需): 详情区块UID
- `fields` (array, 必需): 字段配置列表
  - `name` (string): 字段名
  - `title` (string, 可选): 显示标题
  - `component` (string, 可选): 显示组件
  - `span` (number, 可选): 网格跨度 (1-24)
  - `required` (boolean, 可选): 是否必需

**示例：**
```javascript
await configureDetailsFields(blockUid, {
  fields: [
    { name: 'nickname', title: '昵称', component: 'Input.ReadPretty', span: 12 },
    { name: 'email', title: '邮箱', component: 'Input.ReadPretty', span: 24 }
  ]
});
```

#### `add_details_action` - 添加详情操作按钮
为详情区块添加操作按钮。

**参数：**
- `blockUid` (string, 必需): 详情区块UID
- `actionType` (string, 必需): 操作类型 ('edit' | 'delete' | 'export' | 'custom')
- `title` (string, 必需): 按钮标题
- `icon` (string, 可选): 按钮图标
- `customSettings` (object, 可选): 自定义操作设置

### 4. 关联详情区块工具

#### `add_association_details_block` - 添加关联详情区块
创建显示关联数据的详情区块。

**参数：**
- `parentUid` (string, 必需): 父容器UID
- `collectionName` (string, 必需): 目标集合名称
- `association` (string, 必需): 关联字段 (如 "users.profile")
- `title` (string, 可选): 区块标题
- `dataSource` (string, 可选): 数据源
- `position` (string, 可选): 插入位置

### 5. 高级工具

#### `custom_details_request` - 自定义详情请求
执行自定义的详情相关请求。

**参数：**
- `url` (string, 必需): 请求URL
- `method` (string, 可选): HTTP方法
- `headers` (object, 可选): 请求头
- `params` (object, 可选): URL参数
- `data` (object, 可选): 请求体数据

## 🔧 增强的详情区块模板

新的详情区块模板支持更多配置选项：

```javascript
const blockSchema = detailsTemplate.createSchema({
  collectionName: 'users',
  title: '用户详情',
  includeActions: true,        // 包含操作栏
  includePagination: false,    // 包含分页
  customFields: [              // 自定义字段配置
    { name: 'nickname', title: '昵称', component: 'Input.ReadPretty', span: 12 },
    { name: 'email', title: '邮箱', component: 'Input.ReadPretty', span: 24 }
  ]
});
```

## 🧪 测试验证

运行测试脚本验证功能：

```bash
node tests/test-details-block-tools.js
```

测试包括：
- ✅ 详情数据获取
- ✅ 自定义请求
- ✅ 增强区块Schema生成
- ✅ 关联详情区块Schema生成
- ⚠️ 导出功能（需要服务器端支持）

## 📋 API 映射对照

| 工具功能 | 对应Hook | API方法 | 关键参数 |
|---------|---------|---------|----------|
| 获取详情数据 | `useDetailsBlockProps` | `resource.get()` | `filterByTk`, `fields`, `appends` |
| 更新详情记录 | `useCustomizeUpdateActionProps` | `resource.update()` | `filterByTk`, `values`, `updateAssociationValues` |
| 删除详情记录 | `useDestroyActionProps` | `resource.destroy()` | `filterByTk`, `filter` |
| 自定义请求 | `useCustomizeRequestActionProps` | `apiClient.request()` | `url`, `method`, `headers`, `data` |
| 导出数据 | `useExportActionProps` | `resource.export()` | `filterByTk`, `fields`, `format` |

## 🎉 总结

通过这些新增的工具，NocoBase MCP 现在提供了完整的详情区块操作能力：

1. **数据操作**: 获取、更新、删除详情记录
2. **配置管理**: 字段配置、操作按钮管理
3. **高级功能**: 导出、批量操作、自定义请求
4. **关联支持**: 关联详情区块创建和管理

这些工具完全基于 NocoBase 官方文档实现，确保了与平台的完美兼容性。
