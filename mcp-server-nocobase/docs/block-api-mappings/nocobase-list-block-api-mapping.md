# NocoBase List Block 操作与 API 参数对应关系

本文档详细描述了 NocoBase 中 List Block 的各种操作与其对应的 API 调用参数，为开发者提供完整的参考指南。

## 目录

1. [List Block 架构概述](#1-list-block-架构概述)
2. [基础列表操作](#2-基础列表操作)
3. [列表项操作](#3-列表项操作)
4. [列表数据处理机制](#4-列表数据处理机制)
5. [列表区块配置](#5-列表区块配置)
6. [高级列表操作](#6-高级列表操作)
7. [前端组件配置示例](#7-前端组件配置示例)
8. [参数传递机制](#8-参数传递机制)

---

## 1. List Block 架构概述

### 核心组件结构

```
ListBlockProvider (列表上下文提供者)
├── CardItem (容器组件)
│   ├── List (主列表组件)
│   │   ├── ActionBar (列表操作栏)
│   │   │   ├── Filter/Refresh/Add New Actions (筛选/刷新/新增操作)
│   │   │   ├── Import/Export Actions (导入/导出操作)
│   │   │   └── Custom Actions (自定义操作)
│   │   ├── List.Item (列表项)
│   │   │   ├── Grid (字段容器)
│   │   │   │   └── List Item Fields (动态列表字段)
│   │   │   └── Item ActionBar (项操作栏)
│   │   │       ├── View/Edit/Delete Actions (查看/编辑/删除操作)
│   │   │       └── Custom Item Actions (自定义项操作)
│   │   └── Pagination (分页组件)
└── Block Settings & Configuration (区块配置)
```

### 关键文件位置

- **ListBlockProvider**: `/packages/core/client/src/schema-component/antd/list/List.Decorator.tsx`
- **List Component**: `/packages/core/client/src/schema-component/antd/list/List.tsx`
- **List Action Initializers**: `/packages/core/client/src/modules/blocks/data-blocks/list/ListActionInitializers.tsx`
- **List Item Action Initializers**: `/packages/core/client/src/modules/blocks/data-blocks/list/listItemActionInitializers.tsx`
- **Action Hooks**: `/packages/core/client/src/block-provider/hooks/index.ts`

---

## 2. 基础列表操作

### List Data Loading (列表数据加载)

- **x-action**: `list`
- **x-use-component-props**: `useListBlockProps`
- **API 参数**:
  ```typescript
  {
    page?: number,              // 页码 (默认: 1)
    pageSize?: number,          // 每页条数 (默认: 10)
    filter?: object,            // 过滤条件
    fields?: string[],          // 返回字段
    appends?: string[],         // 关联字段
    except?: string[],          // 排除字段
    sort?: string[],            // 排序
    tree?: boolean              // 是否树形结构
  }
  ```

**源码位置**: `/packages/core/client/src/schema-component/antd/list/List.tsx:64`

**实现逻辑**:
```typescript
const onPaginationChange: PaginationProps['onChange'] = useCallback(
  (page, pageSize) => {
    run({
      ...params?.[0],
      page: page,
      pageSize: pageSize,
    });
  },
  [run, params],
);
```

### Create New Item (新增列表项)

- **x-action**: `create`
- **x-use-component-props**: `useCreateActionProps`
- **API 参数**:
  ```typescript
  {
    values: object,                    // 要创建的数据
    filterKeys?: string[],             // 过滤字段
    updateAssociationValues?: boolean,  // 是否更新关联值
    triggerWorkflows?: string,          // 触发工作流
    overwriteValues?: object,          // 覆盖值
    assignedValues?: object,           // 分配值
  }
  ```

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:237`

### Filter List (筛选列表)

- **x-action**: `filter`
- **x-use-component-props**: `useFilterBlockActionProps`
- **API 参数**:
  ```typescript
  {
    filter: object,         // 筛选条件
    page: number,           // 页码
    pageSize: number,       // 每页条数
  }
  ```

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:567`

### Refresh List (刷新列表)

- **x-action**: `refresh`
- **x-use-component-props**: `useRefreshActionProps`
- **API 参数**:
  ```typescript
  {
    // 无需额外参数，直接调用 service.refresh()
  }
  ```

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:1132`

**实现逻辑**:
```typescript
export const useRefreshActionProps = () => {
  const { service } = useBlockRequestContext();
  return {
    async onClick() {
      service?.refresh?.();
    },
  };
};
```

---

## 3. 列表项操作

### View Item (查看列表项)

- **x-action**: `view`
- **x-use-component-props**: `useViewActionProps`
- **API 参数**:
  ```typescript
  {
    filterByTk: string | number,  // 主键值
    filter?: object,               // 过滤条件
    fields?: string[],             // 返回字段
    appends?: string[],            // 关联字段
    except?: string[],             // 排除字段
  }
  ```

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:616`

### Edit Item (编辑列表项)

- **x-action**: `update`
- **x-use-component-props**: `useUpdateActionProps`
- **API 参数**:
  ```typescript
  {
    filterByTk: string | number,       // 主键值 (必需)
    values: object,                   // 更新的数据
    overwriteValues?: object,         // 覆盖值
    assignedValues?: object,          // 分配值
    updateAssociationValues?: boolean, // 是否更新关联值
    triggerWorkflows?: string,         // 触发工作流
  }
  ```

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:948`

### Delete Item (删除列表项)

- **x-action**: `destroy`
- **x-use-component-props**: `useDestroyActionProps`
- **API 参数**:
  ```typescript
  {
    filterByTk: string | number,  // 主键值 (必需)
    filter?: object               // 过滤条件
  }
  ```

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:1006`

### Bulk Delete (批量删除)

- **x-action**: `destroy`
- **x-use-component-props**: `useBulkDestroyActionProps`
- **API 参数**:
  ```typescript
  {
    filterByTk: string[] | number[],  // 主键值数组 (必需)
    filter?: object                  // 过滤条件
  }
  ```

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:1070`

**实现逻辑**:
```typescript
export const useBulkDestroyActionProps = () => {
  const { field } = useBlockRequestContext();
  const { resource, service } = useBlockRequestContext();
  const { setSubmitted } = useActionContext();
  const collection = useCollection_deprecated();
  const { filterTargetKey } = collection;
  
  return {
    async onClick(e?, callBack?) {
      let filterByTk = field.data?.selectedRowKeys;
      if (Array.isArray(filterTargetKey)) {
        filterByTk = field.data.selectedRowData.map((v) => {
          // 处理复合主键
        });
      }
      
      await resource.destroy({
        filterByTk,
        triggerWorkflows: triggerWorkflows?.length
          ? triggerWorkflows.map((row) => [row.workflowKey, row.context].filter(Boolean).join('!')).join(',')
          : undefined,
      });
      
      __parent?.service?.refresh();
    }
  };
};
```

---

## 4. 列表数据处理机制

### 数据获取流程

**源码位置**: `/packages/core/client/src/schema-component/antd/list/List.Decorator.tsx:23`

```typescript
const InternalListBlockProvider = (props) => {
  const { resource, service } = useBlockRequestContext();
  const field = useField();
  const form = useMemo(() => {
    return createForm({
      readPretty: true,
    });
  }, []);

  useEffect(() => {
    if (!service?.loading) {
      form.query(/\.list$/).forEach((field) => {
        // @ts-ignore
        field.setValue?.(service?.data?.data);
      });
    }
  }, [field.address, form, service?.data?.data, service?.loading]);

  return (
    <ListBlockContext.Provider
      value={{
        service,
        resource,
      }}
    >
      <FormContext.Provider value={form}>
        {props.children}
      </FormContext.Provider>
    </ListBlockContext.Provider>
  );
};
```

### 列表项渲染机制

**源码位置**: `/packages/core/client/src/schema-component/antd/list/List.tsx:164`

```typescript
export const List = withDynamicSchemaProps(InternalList);

const InternalList = (props) => {
  const { service } = useListBlockContext();
  const field = useField<ArrayField>();
  const [schemaMap] = useState(new Map());
  
  const getSchema = useCallback(
    (key) => {
      if (!schemaMap.has(key)) {
        schemaMap.set(
          key,
          new Schema({
            type: 'object',
            properties: {
              [key]: fieldSchema.properties['item'],
            },
          }),
        );
      }
      return schemaMap.get(key);
    },
    [fieldSchema.properties, schemaMap],
  );

  return (
    <AntdList
      {...props}
      pagination={paginationProps}
      loading={service?.loading}
    >
      {field.value?.length
        ? field.value.map((item, index) => {
            return (
              <NocoBaseRecursionField
                basePath={field.address}
                key={index}
                name={index}
                onlyRenderProperties
                schema={getSchema(index)}
              ></NocoBaseRecursionField>
            );
          })
        : null}
    </AntdList>
  );
};
```

### 分页数据处理

**源码位置**: `/packages/core/client/src/schema-component/antd/list/List.tsx:64`

```typescript
const onPaginationChange: PaginationProps['onChange'] = useCallback(
  (page, pageSize) => {
    run({
      ...params?.[0],
      page: page,
      pageSize: pageSize,
    });
  },
  [run, params],
);

const usePagination = () => {
  const meta = service?.data?.meta;
  const { pageSize, count, hasNext, page } = meta || {};
  
  return {
    onChange: onPaginationChange,
    total: count || 0,
    pageSize: pageSize || 10,
    current: page || 1,
    showSizeChanger: true,
    pageSizeOptions: [5, 10, 20, 50, 100, 200],
  };
};
```

---

## 5. 列表区块配置

### 基础列表区块配置

```typescript
{
  type: 'void',
  'x-acl-action': 'users:view',
  'x-decorator': 'List.Decorator',
  'x-use-decorator-props': 'useListBlockDecoratorProps',
  'x-decorator-props': {
    collection: 'users',
    dataSource: 'main',
    readPretty: true,
    action: 'list',
    params: {
      pageSize: 10,
    },
    runWhenParamsChanged: true,
    rowKey: 'id',
  },
  'x-component': 'CardItem',
  'x-toolbar': 'BlockSchemaToolbar',
  'x-settings': 'blockSettings:list',
  properties: {
    actionBar: {
      type: 'void',
      'x-initializer': 'list:configureActions',
      'x-component': 'ActionBar',
      'x-component-props': {
        style: {
          marginBottom: 'var(--nb-spacing)',
        },
      },
    },
    list: {
      type: 'array',
      'x-component': 'List',
      'x-use-component-props': 'useListBlockProps',
      properties: {
        item: {
          type: 'object',
          'x-component': 'List.Item',
          'x-read-pretty': true,
          'x-use-component-props': 'useListItemProps',
          properties: {
            grid: {
              type: 'void',
              'x-component': 'Grid',
              'x-initializer': 'details:configureFields',
            },
            actionBar: {
              type: 'void',
              'x-align': 'left',
              'x-initializer': 'list:configureItemActions',
              'x-component': 'ActionBar',
              'x-use-component-props': 'useListActionBarProps',
              'x-component-props': {
                layout: 'one-column',
              },
            },
          },
        },
      },
    },
  },
}
```

### 关联列表区块配置

```typescript
{
  'x-decorator': 'List.Decorator',
  'x-decorator-props': {
    resource: 'a.o2m',
    collection: 'b',
    association: 'a.o2m',
    readPretty: true,
    action: 'list',
    params: {
      pageSize: 10,
    },
    rowKey: 'id'
  },
  'x-acl-action': 'a.o2m:view',
  properties: {
    list: {
      'x-component': 'List',
      'x-read-pretty': true,
      'x-use-component-props': 'useListBlockProps',
      properties: {
        item: {
          'x-component': 'List.Item',
          'x-read-pretty': true,
          'x-use-component-props': 'useListItemProps',
          properties: {
            // 关联字段配置
          },
        },
      },
    },
  },
}
```

---

## 6. 高级列表操作

### Import List Data (导入列表数据)

- **x-action**: `importXlsx`
- **x-use-component-props**: `useImportActionProps`
- **API 参数**:
  ```typescript
  {
    file: File,                      // 导入的文件
    collection: string,              // 目标集合
    fields?: string[],               // 字段映射
    updateStrategy?: 'upsert' | 'insert' | 'update',  // 更新策略
  }
  ```

**源码位置**: `/packages/core/client/src/modules/blocks/data-blocks/list/ListActionInitializers.tsx:50`

### Export List Data (导出列表数据)

- **x-action**: `export`
- **x-use-component-props**: `useExportActionProps`
- **API 参数**:
  ```typescript
  {
    filter?: object,               // 过滤条件
    fields?: string[],             // 导出字段
    format?: 'csv' | 'xlsx',       // 导出格式
    pageSize?: number,             // 每页条数
    page?: number,                 // 页码
  }
  ```

**源码位置**: `/packages/core/client/src/modules/blocks/data-blocks/list/ListActionInitializers.tsx:64`

### Custom List Request (自定义列表请求)

- **x-action**: `customize:table:request:global`
- **x-use-component-props**: `useCustomizeRequestActionProps`
- **API 参数**:
  ```typescript
  {
    url: string,                      // 请求地址
    method: 'GET' | 'POST' | 'PUT',  // 请求方法
    headers?: object,                 // 请求头
    params?: object,                  // URL 参数
    data?: object,                    // 请求体数据
  }
  ```

**源码位置**: `/packages/core/client/src/modules/blocks/data-blocks/list/ListActionInitializers.tsx:77`

### Custom Item Request (自定义项请求)

- **x-action**: `customize:table:request`
- **x-use-component-props**: `useCustomizeRequestActionProps`
- **API 参数**:
  ```typescript
  {
    url: string,                      // 请求地址
    method: 'GET' | 'POST' | 'PUT',  // 请求方法
    headers?: object,                 // 请求头
    params?: object,                  // URL 参数
    data?: object,                    // 请求体数据
  }
  ```

**源码位置**: `/packages/core/client/src/modules/blocks/data-blocks/list/listItemActionInitializers.tsx:71`

---

## 7. 前端组件配置示例

### 列表操作按钮配置

```typescript
{
  type: 'void',
  'x-component': 'ActionBar',
  'x-initializer': 'list:configureActions',
  properties: {
    filter: {
      type: 'void',
      title: '筛选',
      'x-action': 'filter',
      'x-component': 'Action',
      'x-component-props': {
        icon: 'FilterOutlined',
      },
      'x-settings': 'actionSettings:filter',
    },
    create: {
      type: 'void',
      title: '新增',
      'x-action': 'create',
      'x-component': 'Action',
      'x-use-component-props': 'useCreateActionProps',
      'x-component-props': {
        type: 'primary',
        icon: 'PlusOutlined',
      },
      'x-settings': 'actionSettings:create',
    },
    refresh: {
      type: 'void',
      title: '刷新',
      'x-action': 'refresh',
      'x-component': 'Action',
      'x-use-component-props': 'useRefreshActionProps',
      'x-component-props': {
        icon: 'ReloadOutlined',
      },
    },
    export: {
      type: 'void',
      title: '导出',
      'x-action': 'export',
      'x-component': 'Action',
      'x-use-component-props': 'useExportActionProps',
      'x-component-props': {
        icon: 'ExportOutlined',
      },
    },
    import: {
      type: 'void',
      title: '导入',
      'x-action': 'importXlsx',
      'x-component': 'Action',
      'x-component-props': {
        icon: 'ImportOutlined',
      },
    },
  },
}
```

### 列表项操作按钮配置

```typescript
{
  type: 'void',
  'x-component': 'ActionBar',
  'x-initializer': 'list:configureItemActions',
  'x-component-props': {
    layout: 'one-column',
  },
  properties: {
    view: {
      type: 'void',
      title: '查看',
      'x-action': 'view',
      'x-component': 'Action.Link',
      'x-use-component-props': 'useViewActionProps',
      'x-component-props': {
        icon: 'EyeOutlined',
      },
    },
    edit: {
      type: 'void',
      title: '编辑',
      'x-action': 'update',
      'x-component': 'Action.Link',
      'x-use-component-props': 'useUpdateActionProps',
      'x-component-props': {
        icon: 'EditOutlined',
      },
    },
    delete: {
      type: 'void',
      title: '删除',
      'x-action': 'destroy',
      'x-component': 'Action.Link',
      'x-use-component-props': 'useDestroyActionProps',
      'x-component-props': {
        icon: 'DeleteOutlined',
        confirm: {
          title: '确认删除',
          content: '确定要删除这条记录吗？',
        },
      },
    },
  },
}
```

### 列表字段配置

```typescript
{
  type: 'void',
  'x-component': 'Grid',
  'x-initializer': 'details:configureFields',
  properties: {
    fieldName: {
      type: 'string',
      title: '字段名称',
      'x-decorator': 'FormItem',
      'x-component': 'Input.ReadPretty',
      'x-read-pretty': true,
    },
    relationField: {
      type: 'string',
      title: '关联字段',
      'x-decorator': 'FormItem',
      'x-component': 'RecordPicker.ReadPretty',
      'x-component-props': {
        mode: 'tags',
        fieldNames: {
          label: 'name',
          value: 'id',
        },
      },
    },
  },
}
```

### 自定义操作配置

```typescript
{
  type: 'void',
  title: '自定义操作',
  'x-action': 'customize:table:request:global',
  'x-component': 'Action',
  'x-use-component-props': 'useCustomizeRequestActionProps',
  'x-settings': 'actionSettings:customizeRequest',
  'x-action-settings': {
    requestSettings: {
      url: '/api/custom/list-endpoint',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        // 请求数据模板
      },
    },
    onSuccess: {
      successMessage: '操作成功',
      manualClose: true,
    },
  },
}
```

---

## 8. 参数传递机制

### 列表参数获取流程

**源码位置**: `/packages/core/client/src/schema-component/antd/list/List.Decorator.tsx:66`

```typescript
export const ListBlockProvider = withDynamicSchemaProps((props) => {
  const { params } = props;
  const { filter: parsedFilter, parseVariableLoading } = useParsedFilter({
    filterOption: params?.filter,
  });
  
  const paramsWithFilter = useMemo(() => {
    return {
      ...params,
      filter: parsedFilter,
    };
  }, [parsedFilter, params]);

  // parse filter 的过程是异步的，且一开始 parsedFilter 是一个空对象，所以当 parsedFilter 为空 params.filter 不为空时，
  // 说明 filter 还未解析完成，此时不应该渲染，防止重复请求多次
  if ((_.isEmpty(parsedFilter) && !_.isEmpty(params?.filter)) || parseVariableLoading) {
    return null;
  }

  return (
    <BlockProvider name="list" {...props} params={paramsWithFilter}>
      <InternalListBlockProvider {...props} />
    </BlockProvider>
  );
});
```

### Action Hooks 参数映射

| Hook | Action Type | API Method | Key Parameters |
|------|-------------|------------|----------------|
| `useCreateActionProps` | `create` | `resource.create()` | `values`, `filterKeys`, `updateAssociationValues` |
| `useUpdateActionProps` | `update` | `resource.update()` | `filterByTk`, `values`, `updateAssociationValues` |
| `useDestroyActionProps` | `destroy` | `resource.destroy()` | `filterByTk`, `filter` |
| `useBulkDestroyActionProps` | `destroy` | `resource.destroy()` | `filterByTk[]`, `filter` |
| `useRefreshActionProps` | `refresh` | `service.refresh()` | 无参数 |
| `useFilterBlockActionProps` | `filter` | `doFilter()` | `filter`, `page`, `pageSize` |
| `useCustomizeRequestActionProps` | `customize:table:request` | `apiClient.request()` | `url`, `method`, `headers`, `data` |
| `useExportActionProps` | `export` | `resource.export()` | `filter`, `fields`, `format` |

### 工作流触发参数

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:279`

```typescript
// 工作流触发参数格式
triggerWorkflows: triggerWorkflows?.length
  ? triggerWorkflows.map((row) => [row.workflowKey, row.context].filter(Boolean).join('!')).join(',')
  : undefined,

// 示例: "workflow1!context1,workflow2!context2"
```

### 列表区块数据流

```typescript
// 1. 初始化列表区块
const service = useResourceAction({
  resource: 'users',
  action: 'list',
  params: {
    page: 1,
    pageSize: 10,
    fields: ['id', 'name', 'email'],
    appends: ['profile'],
    filter: { status: 'active' },
  },
});

// 2. 数据请求
GET /api/users:list?page=1&pageSize=10&fields=id,name,email&appends=profile&filter={"status":"active"}

// 3. 数据响应
{
  "data": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "profile": {
        "id": 1,
        "avatar": "avatar.jpg"
      }
    },
    // ... 更多数据
  ],
  "meta": {
    "page": 1,
    "pageSize": 10,
    "total": 100,
    "hasNext": true
  }
}

// 4. 创建新记录
POST /api/users:create
{
  "values": {
    "name": "Jane Smith",
    "email": "<EMAIL>"
  }
}

// 5. 批量删除记录
POST /api/users:destroy
{
  "filterByTk": [1, 2, 3]
}
```

---

## 总结

本文档详细描述了 NocoBase List Block 的完整操作体系，包括：

1. **基础列表操作**: 数据加载、新增、筛选、刷新
2. **列表项操作**: 查看、编辑、删除、批量删除
3. **数据处理机制**: 数据获取、项渲染、分页处理
4. **列表区块配置**: 基础配置、关联配置、操作配置
5. **高级操作**: 导入、导出、自定义请求
6. **前端配置**: 操作按钮、项操作、字段配置
7. **参数传递**: 从前端到后端的完整数据流

所有代码示例都基于 NocoBase 最新源码，确保了技术准确性和实用性。开发者可以基于这份文档准确理解和使用 NocoBase 的 List Block 功能。

---

**文档版本**: v1.0  
**更新日期**: 2024-08-09  
**NocoBase 版本**: 基于最新源码分析