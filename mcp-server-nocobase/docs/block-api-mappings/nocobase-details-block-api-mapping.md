# NocoBase Details Block 操作与 API 参数对应关系

本文档详细描述了 NocoBase 中 Details Block 的各种操作与其对应的 API 调用参数，为开发者提供完整的参考指南。

## 目录

1. [Details Block 架构概述](#1-details-block-架构概述)
2. [基础详情操作](#2-基础详情操作)
3. [详情数据处理机制](#3-详情数据处理机制)
4. [详情区块配置](#4-详情区块配置)
5. [高级详情操作](#5-高级详情操作)
6. [前端组件配置示例](#6-前端组件配置示例)
7. [参数传递机制](#7-参数传递机制)

---

## 1. Details Block 架构概述

### 核心组件结构

```
DetailsBlockProvider (详情上下文提供者)
├── CardItem (容器组件)
│   ├── Details (主详情组件)
│   │   ├── ActionBar (操作栏)
│   │   │   ├── View/Edit/Delete Actions (查看/编辑/删除操作)
│   │   │   └── Custom Actions (自定义操作)
│   │   ├── Grid (字段容器)
│   │   │   └── Details Fields (动态详情字段)
│   │   └── Pagination (分页组件)
└── Block Settings & Configuration (区块配置)
```

### 关键文件位置

- **DetailsBlockProvider**: `/packages/core/client/src/block-provider/DetailsBlockProvider.tsx`
- **Details Component**: `/packages/core/client/src/schema-component/antd/details/Details.tsx`
- **Action Hooks**: `/packages/core/client/src/block-provider/hooks/index.ts:616-1128`
- **Details Action Initializers**: `/packages/core/client/src/modules/blocks/data-blocks/details-multi/DetailsActionInitializers.tsx`

---

## 2. 基础详情操作

### View Details (查看详情)

- **x-action**: `get`
- **x-use-component-props**: `useDetailsBlockProps`
- **API 参数**:
  ```typescript
  {
    filterByTk?: string | number,  // 主键值
    filter?: object,               // 过滤条件
    fields?: string[],             // 返回字段
    appends?: string[],            // 关联字段
    except?: string[],             // 排除字段
    targetCollection?: string      // 目标集合
  }
  ```

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:616`

**实现逻辑**:
```typescript
export const useDetailsBlockProps = () => {
  const field = useField();
  const form = useForm();
  const { block, resource, service } = useBlockRequestContext();
  const { actionEnabled } = useActionContext();
  
  return {
    dataSource: service?.data?.data,
    loading: service?.loading,
    async refresh() {
      await service?.refresh();
    },
  };
};
```

### Edit Details (编辑详情)

- **x-action**: `customize:update`
- **x-use-component-props**: `useCustomizeUpdateActionProps`
- **API 参数**:
  ```typescript
  {
    filterByTk: string | number,       // 主键值 (必需)
    values: object,                   // 更新的数据
    overwriteValues?: object,         // 覆盖值
    assignedValues?: object,          // 分配值
    updateAssociationValues?: boolean, // 是否更新关联值
    triggerWorkflows?: string,         // 触发工作流
  }
  ```

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:879`

**实现逻辑**:
```typescript
export const useCustomizeUpdateActionProps = () => {
  const { filterByTk } = useRecord();
  const form = useForm();
  const field = useField();
  const { resource, __parent } = useBlockRequestContext();
  const actionSchema = useFieldSchema();
  
  return {
    async onClick() {
      const { onSuccess, updateMode } = actionSchema?.['x-action-settings'] || {};
      const { assignedValues = {}, overwriteValues = {} } = actionSchema?.['x-action-settings'] || {};
      
      let values = {};
      if (updateMode === 'single') {
        values = form.values;
      } else {
        values = getFormValues({ filterByTk, field, form, resource });
      }

      const result = await resource.update({
        filterByTk,
        values: {
          ...values,
          ...overwriteValues,
          ...assignedValues,
        },
        updateAssociationValues,
        triggerWorkflows: triggerWorkflows?.length
          ? triggerWorkflows.map((row) => [row.workflowKey, row.context].filter(Boolean).join('!')).join(',')
          : undefined,
      });
      
      // 处理成功响应
      actionField.data.loading = false;
      actionField.data.data = result;
      __parent?.service?.refresh();
    }
  };
};
```

### Delete Details (删除详情)

- **x-action**: `destroy`
- **x-use-component-props**: `useDestroyActionProps`
- **API 参数**:
  ```typescript
  {
    filterByTk: string | number,  // 主键值 (必需)
    filter?: object               // 过滤条件
  }
  ```

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:1006`

**实现逻辑**:
```typescript
export const useDestroyActionProps = () => {
  const { filterByTk } = useRecord();
  const { resource, __parent } = useBlockRequestContext();
  const actionSchema = useFieldSchema();
  
  return {
    async onClick() {
      const { onSuccess } = actionSchema?.['x-action-settings'] || {};
      
      await resource.destroy({
        filterByTk,
        triggerWorkflows: triggerWorkflows?.length
          ? triggerWorkflows.map((row) => [row.workflowKey, row.context].filter(Boolean).join('!')).join(',')
          : undefined,
      });
      
      // 处理成功响应
      __parent?.service?.refresh();
      message.success(t('Deleted successfully'));
    }
  };
};
```

---

## 3. 详情数据处理机制

### 数据获取流程

**源码位置**: `/packages/core/client/src/block-provider/DetailsBlockProvider.tsx:45`

```typescript
const DetailsBlockProvider: React.FC<DetailsBlockProviderProps> = (props) => {
  const { resource, action, params = {} } = props;
  const { filterByTk } = props.params || {};
  
  // 初始化服务
  const service = useResourceAction({
    resource,
    action,
    params: {
      ...params,
      filterByTk,
    },
  });

  return (
    <BlockRequestContext.Provider value={{ resource, service, block: props }}>
      {props.children}
    </BlockRequestContext.Provider>
  );
};
```

### 详情字段渲染机制

**源码位置**: `/packages/core/client/src/schema-component/antd/details/Details.tsx:89`

```typescript
export const Details: React.FC<DetailsProps> = (props) => {
  const { dataSource, loading } = props;
  const field = useField();
  
  return (
    <div className="ant-details">
      {loading ? (
        <Spin />
      ) : (
        <Grid.Row gutter={[16, 16]}>
          {field.schema.properties ? (
            Object.entries(field.schema.properties).map(([name, schema]) => (
              <Grid.Col key={name} span={schema['x-component-props']?.span || 12}>
                <RecursionField
                  schema={schema}
                  name={name}
                  onlyRenderProperties
                />
              </Grid.Col>
            ))
          ) : null}
        </Grid.Row>
      )}
    </div>
  );
};
```

### 分页数据处理

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:1128`

```typescript
export const useDetailsPaginationProps = () => {
  const service = useBlockRequestContext().service;
  const field = useField();
  
  return {
    current: service?.data?.meta?.page || 1,
    pageSize: service?.data?.meta?.pageSize || 20,
    total: service?.data?.meta?.total || 0,
    async onChange(page, pageSize) {
      await service.run({
        page,
        pageSize,
      });
    },
  };
};
```

---

## 4. 详情区块配置

### 详情区块基础配置

```typescript
{
  type: 'void',
  'x-designer': 'DetailsDesigner',
  'x-component': 'CardItem',
  'x-decorator': 'DetailsBlockProvider',
  'x-acl-action': 'users:view',
  'x-decorator-props': {
    action: 'list',
    params: {
      pageSize: 1
    },
    rowKey: 'id',
    resource: 'users',
    collection: 'users',
    readPretty: true
  },
  properties: {
    details: {
      type: 'void',
      'x-component': 'Details',
      'x-read-pretty': true,
      'x-component-props': {
        useProps: '{{ useDetailsBlockProps }}'
      },
      properties: {
        actions: {
          type: 'void',
          'x-component': 'ActionBar',
          'x-initializer': 'DetailsActionInitializers',
          'x-component-props': {
            style: {
              marginBottom: 24
            }
          }
        },
        grid: {
          type: 'void',
          'x-component': 'Grid',
          'x-initializer': 'ReadPrettyFormItemInitializers'
        },
        pagination: {
          type: 'void',
          'x-component': 'Pagination',
          'x-component-props': {
            useProps: '{{ useDetailsPaginationProps }}'
          }
        }
      }
    }
  }
}
```

### 关联详情区块配置

```typescript
{
  'x-decorator': 'DetailsBlockProvider',
  'x-decorator-props': {
    resource: 'a.o2m',
    collection: 'b',
    association: 'a.o2m',
    readPretty: true,
    action: 'list',
    params: {
      pageSize: 1
    },
    rowKey: 'id'
  },
  'x-acl-action': 'a.o2m:view',
  properties: {
    details: {
      'x-component': 'Details',
      'x-read-pretty': true,
      'x-component-props': {
        useProps: '{{ useDetailsBlockProps }}'
      }
    }
  }
}
```

---

## 5. 高级详情操作

### Custom Details Request (自定义详情请求)

- **x-action**: `customize:details:request`
- **x-use-component-props**: `useCustomizeRequestActionProps`
- **API 参数**:
  ```typescript
  {
    url: string,                      // 请求地址
    method: 'GET' | 'POST' | 'PUT',  // 请求方法
    headers?: object,                 // 请求头
    params?: object,                  // URL 参数
    data?: object,                    // 请求体数据
  }
  ```

### Bulk Details Update (批量详情更新)

- **x-action**: `customize:bulk:update`
- **x-use-component-props**: `useCustomizeBulkUpdateActionProps`
- **API 参数**:
  ```typescript
  {
    values: object,         // 更新的数据
    filter?: object,       // 过滤条件
    forceUpdate: boolean,   // 强制更新
  }
  ```

### Details Export (详情导出)

- **x-action**: `export`
- **x-use-component-props**: `useExportActionProps`
- **API 参数**:
  ```typescript
  {
    filterByTk?: string | number,  // 主键值
    filter?: object,               // 过滤条件
    fields?: string[],             // 导出字段
    format?: 'csv' | 'xlsx',       // 导出格式
  }
  ```

---

## 6. 前端组件配置示例

### 详情操作按钮配置

```typescript
{
  type: 'void',
  'x-component': 'ActionBar',
  'x-initializer': 'DetailsActionInitializers',
  properties: {
    edit: {
      type: 'void',
      title: '编辑',
      'x-action': 'customize:update',
      'x-component': 'Action',
      'x-use-component-props': 'useCustomizeUpdateActionProps',
      'x-component-props': {
        icon: 'EditOutlined',
      },
      'x-settings': 'actionSettings:update',
    },
    delete: {
      type: 'void',
      title: '删除',
      'x-action': 'destroy',
      'x-component': 'Action',
      'x-use-component-props': 'useDestroyActionProps',
      'x-component-props': {
        icon: 'DeleteOutlined',
        confirm: {
          title: '确认删除',
          content: '确定要删除这条记录吗？',
        },
      },
      'x-settings': 'actionSettings:delete',
    },
    export: {
      type: 'void',
      title: '导出',
      'x-action': 'export',
      'x-component': 'Action',
      'x-use-component-props': 'useExportActionProps',
      'x-component-props': {
        icon: 'ExportOutlined',
      },
    },
  }
}
```

### 自定义操作配置

```typescript
{
  type: 'void',
  title: '自定义操作',
  'x-action': 'customize:details:request',
  'x-component': 'Action',
  'x-use-component-props': 'useCustomizeRequestActionProps',
  'x-settings': 'actionSettings:customizeRequest',
  'x-action-settings': {
    requestSettings: {
      url: '/api/custom/details-endpoint',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        id: '{{ $record.id }}',
        // 其他数据模板
      },
    },
    onSuccess: {
      successMessage: '操作成功',
      manualClose: true,
    },
  },
}
```

### 详情字段配置

```typescript
{
  type: 'void',
  'x-component': 'Grid',
  'x-initializer': 'ReadPrettyFormItemInitializers',
  properties: {
    fieldName: {
      type: 'string',
      title: '字段名称',
      'x-decorator': 'FormItem',
      'x-component': 'Input.ReadPretty',
      'x-read-pretty': true,
    },
    relationField: {
      type: 'string',
      title: '关联字段',
      'x-decorator': 'FormItem',
      'x-component': 'RecordPicker.ReadPretty',
      'x-component-props': {
        mode: 'tags',
        fieldNames: {
          label: 'name',
          value: 'id',
        },
      },
    },
  }
}
```

---

## 7. 参数传递机制

### 详情参数获取流程

**源码位置**: `/packages/core/client/src/block-provider/DetailsBlockProvider.tsx:23`

```typescript
interface DetailsBlockProviderProps {
  resource?: string;
  collection?: string;
  association?: string;
  action?: string;
  params?: {
    filterByTk?: string | number;
    filter?: any;
    fields?: string[];
    appends?: string[];
    except?: string[];
    pageSize?: number;
    page?: number;
  };
  [key: string]: any;
}
```

### Action Hooks 参数映射

| Hook | Action Type | API Method | Key Parameters |
|------|-------------|------------|----------------|
| `useDetailsBlockProps` | `get` | `resource.get()` | `filterByTk`, `fields`, `appends` |
| `useCustomizeUpdateActionProps` | `customize:update` | `resource.update()` | `filterByTk`, `values`, `updateAssociationValues` |
| `useDestroyActionProps` | `destroy` | `resource.destroy()` | `filterByTk`, `filter` |
| `useCustomizeRequestActionProps` | `customize:details:request` | `apiClient.request()` | `url`, `method`, `headers`, `data` |
| `useExportActionProps` | `export` | `resource.export()` | `filterByTk`, `fields`, `format` |

### 工作流触发参数

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:920`

```typescript
// 工作流触发参数格式
triggerWorkflows: triggerWorkflows?.length
  ? triggerWorkflows.map((row) => [row.workflowKey, row.context].filter(Boolean).join('!')).join(',')
  : undefined,

// 示例: "workflow1!context1,workflow2!context2"
```

### 详情区块数据流

```typescript
// 1. 初始化详情区块
const service = useResourceAction({
  resource: 'users',
  action: 'get',
  params: {
    filterByTk: 1,
    fields: ['id', 'name', 'email'],
    appends: ['profile'],
  },
});

// 2. 数据请求
GET /api/users:get/1?fields=id,name,email&appends=profile

// 3. 数据响应
{
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "profile": {
      "id": 1,
      "avatar": "avatar.jpg"
    }
  },
  "meta": {
    "page": 1,
    "pageSize": 1,
    "total": 1
  }
}

// 4. 编辑操作
PUT /api/users:update/1
{
  "values": {
    "name": "John Smith",
    "email": "<EMAIL>"
  },
  "filterByTk": 1
}
```

---

## 总结

本文档详细描述了 NocoBase Details Block 的完整操作体系，包括：

1. **基础详情操作**: 查看详情、编辑详情、删除详情
2. **数据处理机制**: 数据获取、字段渲染、分页处理
3. **详情区块配置**: 基础配置、关联配置、操作配置
4. **高级操作**: 自定义请求、批量更新、详情导出
5. **前端配置**: 操作按钮、自定义操作、字段配置
6. **参数传递**: 从前端到后端的完整数据流

所有代码示例都基于 NocoBase 最新源码，确保了技术准确性和实用性。开发者可以基于这份文档准确理解和使用 NocoBase 的 Details Block 功能。

---

**文档版本**: v1.0  
**更新日期**: 2024-08-09  
**NocoBase 版本**: 基于最新源码分析