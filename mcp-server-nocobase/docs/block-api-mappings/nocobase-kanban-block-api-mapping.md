# NocoBase Kanban Block 操作与 API 调用对应关系分析报告

## 概述

本报告深入分析 NocoBase Kanban Block（看板块）的各种操作如何通过内置 API 进行调用，重点关注操作配置、API 映射关系和实际应用场景。

## 1. Kanban Block 架构概览

### 1.1 核心组件结构

```
KanbanBlockProvider (看板数据提供器)
├── ActionBar (操作栏)
│   ├── Filter (筛选)
│   ├── Add New (新增)
│   └── Custom Request (自定义请求)
└── Kanban (看板容器)
    ├── Column (列容器)
    │   ├── ColumnHeader (列标题)
    │   ├── CardAdder (卡片添加器)
    │   └── Card (卡片)
    │       ├── CardContent (卡片内容)
    │       └── CardActions (卡片操作)
    └── Kanban.CardViewer (卡片查看器)
```

### 1.2 主要文件位置

- **块初始化器**: `/packages/plugins/@nocobase/plugin-kanban/src/client/KanbanBlockInitializer.tsx`
- **操作初始化器**: `/packages/plugins/@nocobase/plugin-kanban/src/client/KanbanActionInitializers.tsx`
- **UI Schema 创建**: `/packages/plugins/@nocobase/plugin-kanban/src/client/createKanbanBlockUISchema.ts`
- **主要组件**: `/packages/plugins/@nocobase/plugin-kanban/src/client/Kanban.tsx`
- **看板逻辑**: `/packages/plugins/@nocobase/plugin-kanban/src/client/board/`
- **排序字段**: `/packages/plugins/@nocobase/plugin-kanban/src/client/CreateAndSelectSort/index.tsx`

## 2. 块级别操作与 API 映射

### 2.1 筛选操作 (Filter)

**操作配置**:
```typescript
{
  name: 'filter',
  title: "{{t('Filter')}}",
  Component: 'FilterActionInitializer',
  schema: {
    'x-align': 'left',
  },
}
```

**API 调用**:
- **端点**: `GET /{collection}:list`
- **参数**: 通过 `filter` 参数传递筛选条件
- **权限**: 无特殊权限要求

**实际应用**:
```typescript
// 筛选操作调用示例
const applyKanbanFilter = async (collectionName, filterParams) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:list`,
    method: 'GET',
    params: {
      filter: filterParams,
      paginate: false, // Kanban 默认不分页
    }
  });
};
```

### 2.2 新增操作 (Add New)

**操作配置**:
```typescript
{
  name: 'addNew',
  title: "{{t('Add new')}}",
  Component: 'CreateActionInitializer',
  schema: {
    'x-align': 'right',
    'x-decorator': 'ACLActionProvider',
    'x-acl-action-props': {
      skipScopeCheck: true,
    },
  },
  useVisible: () => useActionAvailable('create'),
}
```

**API 调用**:
- **端点**: `POST /{collection}:create`
- **权限**: `create`
- **特点**: 自动设置分组字段值

**实际应用**:
```typescript
// 新增卡片调用示例
const createKanbanCard = async (collectionName, cardData, groupField, columnValue) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:create`,
    method: 'POST',
    data: {
      values: {
        ...cardData,
        [groupField]: columnValue, // 自动设置分组字段
      },
      whitelist: ['title', 'description', groupField],
      updateAssociationValues: true,
    }
  });
};
```

### 2.3 自定义请求 (Custom Request)

**操作配置**:
```typescript
{
  name: 'customRequest',
  title: '{{t("Custom request")}}',
  Component: 'CustomRequestInitializer',
  schema: {
    'x-action': 'customize:table:request:global',
  },
}
```

**API 调用**:
- **端点**: `POST /customRequests:send/{requestId}`
- **权限**: 自定义
- **灵活性**: 高

**实际应用**:
```typescript
// 自定义请求调用示例
const sendKanbanCustomRequest = async (requestId, cardData) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/customRequests:send/${requestId}`,
    method: 'POST',
    data: {
      currentRecord: {
        data: cardData,
      },
      $nForm: formData,
      $nSelectedRecord: selectedCard,
    }
  });
};
```

## 3. 卡片级别操作与 API 映射

### 3.1 查看操作 (View)

**操作配置**:
```typescript
{
  type: 'void',
  title: '{{ t("View") }}',
  'x-designer': 'Action.Designer',
  'x-component': 'Kanban.CardViewer',
  'x-action': 'view',
  'x-component-props': {
    openMode: 'drawer',
  }
}
```

**API 调用**:
- **端点**: `GET /{collection}:get/{id}`
- **权限**: `get`
- **参数**: `filterByTk` (主键值)

**实际应用**:
```typescript
// 查看卡片调用示例
const viewKanbanCard = async (collectionName, cardId) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:get`,
    method: 'GET',
    params: {
      filterByTk: cardId,
      fields: ['id', 'title', 'description', 'status'],
      appends: ['relationField'],
    }
  });
};
```

### 3.2 编辑操作 (Edit)

**操作配置**:
```typescript
{
  // 通过 Kanban.Card 组件内部的表单系统处理
  'x-component': 'Kanban.Card',
  'x-read-pretty': true,
  'x-component-props': {
    openMode: 'drawer',
  }
}
```

**API 调用**:
- **端点**: `PUT /{collection}:update/{id}`
- **权限**: `update`
- **参数**: `filterByTk`, `values`

**实际应用**:
```typescript
// 编辑卡片调用示例
const updateKanbanCard = async (collectionName, cardId, updateData) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:update`,
    method: 'PUT',
    data: {
      filterByTk: cardId,
      values: updateData,
      whitelist: ['title', 'description', 'status'],
      updateAssociationValues: true,
    }
  });
};
```

### 3.3 删除操作 (Delete)

**操作配置**:
```typescript
// 通过卡片操作菜单或删除按钮触发
// 使用标准的 NocoBase 删除操作流程
```

**API 调用**:
- **端点**: `DELETE /{collection}:destroy/{id}`
- **权限**: `destroy`
- **参数**: `filterByTk`

**实际应用**:
```typescript
// 删除卡片调用示例
const deleteKanbanCard = async (collectionName, cardId) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:destroy`,
    method: 'DELETE',
    data: {
      filterByTk: cardId,
    }
  });
};
```

## 4. 拖拽操作与 API 映射

### 4.1 卡片移动 (Card Move)

**操作配置**:
```typescript
// 通过 react-beautiful-dnd 处理拖拽
// onCardDragEnd 回调处理移动逻辑
```

**API 调用**:
- **端点**: `POST /{collection}:move`
- **权限**: `update`
- **特殊操作**: 移动排序字段

**实际应用**:
```typescript
// 卡片移动调用示例
const moveKanbanCard = async (collectionName, moveData) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:move`,
    method: 'POST',
    data: {
      sourceId: moveData.sourceCard.id,
      sortField: moveData.sortField || `${moveData.groupField}_sort`,
      targetId: moveData.targetCard?.id, // 可选
      targetScope: {
        [moveData.groupField]: moveData.toColumnId // 跨列移动时
      }
    }
  });
};
```

### 4.2 列重排序 (Column Reorder)

**操作配置**:
```typescript
// 通过列的拖拽功能处理
// 更新列的排序字段
```

**API 调用**:
- **端点**: `PUT /{collection}:update/{id}`
- **权限**: `update`
- **参数**: 排序字段值

**实际应用**:
```typescript
// 列重排序调用示例
const reorderKanbanColumn = async (collectionName, columnId, newSortValue) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:update`,
    method: 'PUT',
    data: {
      filterByTk: columnId,
      values: {
        sort: newSortValue,
      }
    }
  });
};
```

## 5. Kanban Block 配置与创建

### 5.1 UI Schema 创建

**源码位置**: `/packages/plugins/@nocobase/plugin-kanban/src/client/createKanbanBlockUISchema.ts:13`

```typescript
export const createKanbanBlockUISchema = (options: {
  groupField: string;
  sortField: string;
  dataSource: string;
  params?: Record<string, any>;
  collectionName?: string;
  association?: string;
}): ISchema => {
  const { collectionName, groupField, sortField, dataSource, params, association } = options;

  return {
    type: 'void',
    'x-acl-action': `${association || collectionName}:list`,
    'x-decorator': 'KanbanBlockProvider',
    'x-decorator-props': {
      collection: collectionName,
      dataSource,
      action: 'list',
      groupField,
      sortField,
      params: {
        paginate: false, // Kanban 默认不分页
        ...params,
      },
    },
    'x-toolbar': 'BlockSchemaToolbar',
    'x-settings': 'blockSettings:kanban',
    'x-component': 'CardItem',
    properties: {
      actions: {
        type: 'void',
        'x-initializer': 'kanban:configureActions',
        'x-component': 'ActionBar',
        'x-component-props': {
          style: {
            marginBottom: 'var(--nb-spacing)',
          },
        },
      },
      [uid()]: {
        type: 'array',
        'x-component': 'Kanban',
        'x-use-component-props': 'useKanbanBlockProps',
        properties: {
          card: {
            type: 'void',
            'x-read-pretty': true,
            'x-label-disabled': true,
            'x-decorator': 'BlockItem',
            'x-component': 'Kanban.Card',
            'x-component-props': {
              openMode: 'drawer',
            },
            properties: {
              grid: {
                type: 'void',
                'x-component': 'Grid',
                'x-component-props': { dndContext: false },
              },
            },
          },
          cardViewer: {
            type: 'void',
            title: '{{ t("View") }}',
            'x-component': 'Kanban.CardViewer',
            'x-action': 'view',
            'x-component-props': {
              openMode: 'drawer',
            },
          },
        },
      },
    },
  };
};
```

### 5.2 通过 API 创建 Kanban Block

```typescript
// 创建 Kanban Block 调用示例
const createKanbanBlock = async (targetUid, blockConfig) => {
  const apiClient = useAPIClient();
  
  return await apiClient.request({
    url: `/uiSchemas:insertAdjacent/${targetUid}`,
    method: 'POST',
    data: {
      position: 'beforeEnd',
      schema: {
        type: 'void',
        title: blockConfig.title || 'Kanban',
        'x-decorator': 'KanbanBlockProvider',
        'x-component': 'CardItem',
        'x-settings': 'blockSettings:kanban',
        'x-decorator-props': {
          collection: blockConfig.collectionName,
          dataSource: blockConfig.dataSource,
          action: 'list',
          groupField: blockConfig.groupField,
          sortField: blockConfig.sortField,
          params: {
            paginate: false,
            sort: [blockConfig.sortField],
          },
        },
        properties: {
          actions: {
            type: 'void',
            'x-initializer': 'kanban:configureActions',
            'x-component': 'ActionBar',
          },
          kanban: {
            type: 'array',
            'x-component': 'Kanban',
            properties: {
              card: {
                type: 'void',
                'x-component': 'Kanban.Card',
                'x-component-props': {
                  openMode: 'drawer',
                },
              },
            },
          },
        },
      }
    }
  });
};
```

## 6. 排序字段管理

### 6.1 创建排序字段

**源码位置**: `/packages/plugins/@nocobase/plugin-kanban/src/client/CreateAndSelectSort/index.tsx:54`

```typescript
// 创建排序字段调用示例
const createSortField = async (collectionName, fieldData) => {
  const apiClient = useAPIClient();
  const { data } = await apiClient.resource('collections.fields', collectionName).create({
    values: {
      type: 'sort',
      interface: 'sort',
      ...fieldData,
    },
  });
  return data.data;
};
```

### 6.2 更新排序字段（第三方数据源）

```typescript
// 更新排序字段调用示例
const updateSortField = async (dataSource, collectionName, fieldName, fieldData) => {
  const apiClient = useAPIClient();
  const { data } = await apiClient.request({
    url: `dataSourcesCollections/${dataSource}.${collectionName}/fields:update?filterByTk=${fieldName}`,
    method: 'post',
    data: { 
      type: 'sort', 
      interface: 'sort', 
      ...fieldData 
    },
  });
  return data.data;
};
```

## 7. 权限控制与 ACL

### 7.1 权限映射表

| 操作 | x-action | 权限要求 | ACL Provider |
|------|----------|----------|--------------|
| Filter | filter | 无 | 否 |
| Add New | create | create | 是 |
| Custom Request | customize:table:request:global | 自定义 | 否 |
| View | get | get | 是 |
| Edit | update | update | 是 |
| Delete | destroy | destroy | 是 |
| Move Card | move | update | 是 |
| Create Sort Field | create | create | 是 |

### 7.2 ACL 权限检查

```typescript
// 权限检查 Hook
const useActionAvailable = (action: string) => {
  const collection = useCollection();
  const { unavailableActions, availableActions } = collection?.options || {};
  
  if (availableActions) {
    return availableActions.includes(action);
  }
  
  if (unavailableActions) {
    return !unavailableActions.includes(action);
  }
  
  return true;
};
```

## 8. 实际应用示例

### 8.1 完整的 Kanban Block 配置

```typescript
// 完整配置示例
const kanbanConfig = {
  // 块配置
  title: '任务看板',
  collectionName: 'tasks',
  dataSource: 'main',
  groupField: 'status',
  sortField: 'priority_sort',
  
  // 块级别操作
  blockActions: [
    {
      name: 'filter',
      title: '筛选',
      enabled: true,
    },
    {
      name: 'addNew',
      title: '新增任务',
      enabled: true,
      requiresAuth: true,
    },
    {
      name: 'customRequest',
      title: '批量操作',
      enabled: true,
    },
  ],
  
  // 字段配置
  fields: [
    {
      name: 'title',
      title: '任务标题',
      component: 'Input',
    },
    {
      name: 'description',
      title: '任务描述',
      component: 'Input.TextArea',
    },
    {
      name: 'status',
      title: '状态',
      component: 'Select',
      options: [
        { label: '待办', value: 'todo' },
        { label: '进行中', value: 'doing' },
        { label: '已完成', value: 'done' },
      ],
    },
  ],
};

// 创建 Kanban Block
const createTaskKanban = async (targetUid) => {
  return await createKanbanBlock(targetUid, kanbanConfig);
};
```

### 8.2 拖拽操作处理

```typescript
// 拖拽操作处理示例
const handleCardDragEnd = async (result, collectionName, groupField, sortField) => {
  if (!result.destination) return;
  
  const apiClient = useAPIClient();
  
  try {
    // 移动卡片
    await apiClient.request({
      url: `/${collectionName}:move`,
      method: 'POST',
      data: {
        sourceId: result.draggableId,
        sortField: sortField,
        targetId: result.destination.droppableId,
        targetScope: {
          [groupField]: result.destination.droppableId
        }
      }
    });
    
    // 刷新看板数据
    refreshKanbanData();
  } catch (error) {
    console.error('拖拽操作失败:', error);
    // 回滚操作
    rollbackCardMove();
  }
};
```

## 9. 性能优化建议

### 9.1 数据加载优化

```typescript
// 优化数据加载参数
const optimizedKanbanParams = {
  paginate: false, // 看板不分页
  fields: ['id', 'title', 'description', 'status', 'priority_sort'],
  appends: ['assignee'], // 预加载关联数据
  filter: {}, // 应用筛选条件
  sort: ['priority_sort'], // 排序规则
};
```

### 9.2 拖拽性能优化

```typescript
// 使用防抖优化拖拽操作
const debouncedCardMove = debounce(async (moveData) => {
  const apiClient = useAPIClient();
  await apiClient.request({
    url: `/${moveData.collectionName}:move`,
    method: 'POST',
    data: moveData,
  });
}, 300);
```

### 9.3 批量操作

```typescript
// 批量更新卡片状态
const batchUpdateCardStatus = async (collectionName, cardIds, newStatus) => {
  const apiClient = useAPIClient();
  
  // 使用事务确保一致性
  const transaction = await apiClient.request({
    url: '/transactions:start',
    method: 'POST'
  });
  
  try {
    const results = [];
    for (const cardId of cardIds) {
      const result = await apiClient.request({
        url: `/${collectionName}:update`,
        method: 'PUT',
        data: {
          filterByTk: cardId,
          values: { status: newStatus },
        },
        headers: {
          'X-Transaction-ID': transaction.data.id
        }
      });
      results.push(result);
    }
    
    await apiClient.request({
      url: `/transactions:commit/${transaction.data.id}`,
      method: 'POST'
    });
    
    return results;
  } catch (error) {
    await apiClient.request({
      url: `/transactions:rollback/${transaction.data.id}`,
      method: 'POST'
    });
    throw error;
  }
};
```

## 10. 错误处理和调试

### 10.1 常见错误及解决方案

| 错误类型 | 可能原因 | 解决方案 |
|---------|----------|----------|
| 401 Unauthorized | 认证失败 | 检查 JWT token 是否有效 |
| 403 Forbidden | 权限不足 | 检查用户权限配置 |
| 404 Not Found | 资源不存在 | 验证集合名称和字段配置 |
| 422 Unprocessable Entity | 参数错误 | 检查请求参数格式 |
| 500 Internal Server Error | 服务器错误 | 检查服务器日志 |

### 10.2 调试工具

```typescript
// 调试 Kanban API 调用
const debugKanbanApi = async (collectionName, operation, params) => {
  console.log('Kanban API Request:', {
    collectionName,
    operation,
    params,
    timestamp: new Date().toISOString(),
  });
  
  try {
    const apiClient = useAPIClient();
    const response = await apiClient.request({
      url: `/${collectionName}:${operation}`,
      method: operation === 'list' ? 'GET' : 'POST',
      data: params,
    });
    
    console.log('Kanban API Response:', {
      status: response.status,
      data: response.data,
      timestamp: new Date().toISOString(),
    });
    
    return response;
  } catch (error) {
    console.error('Kanban API Error:', {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message,
      timestamp: new Date().toISOString(),
    });
    throw error;
  }
};
```

## 11. 总结

### 11.1 Kanban Block 操作特点

1. **可视化拖拽**: 支持卡片在列之间的拖拽移动
2. **实时同步**: 拖拽操作自动同步到后端
3. **灵活配置**: 可配置分组字段和排序字段
4. **权限控制**: 细粒度的 ACL 权限管理
5. **响应式设计**: 适应不同屏幕尺寸

### 11.2 API 调用模式

1. **统一端点**: 所有操作都通过标准 RESTful API
2. **特殊操作**: `move` 操作专门处理拖拽移动
3. **字段管理**: 支持动态创建排序字段
4. **事务支持**: 批量操作支持事务处理

### 11.3 最佳实践

1. **合理设置分组字段**: 选择合适的单选或下拉字段作为分组字段
2. **优化排序字段**: 使用专门的排序字段提高拖拽性能
3. **权限配置**: 根据用户角色合理配置操作权限
4. **错误处理**: 实现完善的错误处理和回滚机制
5. **性能优化**: 对于大量数据使用分块加载和虚拟滚动

Kanban Block 为 NocoBase 提供了强大的可视化任务管理能力，通过拖拽操作和实时同步，可以满足各种看板式业务场景的需求。开发者可以基于这份文档准确理解和使用 Kanban Block 的各种功能。

---

**报告日期**: 2025-01-09  
**版本**: NocoBase 最新版本  
**作者**: Claude Code Assistant  

**注意**: 本报告基于 NocoBase 最新源码分析，代码示例已与实际实现保持一致。