# NocoBase MCP 看板操作工具指南

本文档介绍了为 NocoBase MCP 服务器新增的看板操作（Kanban Operations）相关工具能力，这些工具基于 [NocoBase Kanban Block API 映射文档](../block-api-mappings/nocobase-kanban-block-api-mapping.md) 实现。

## 🎯 新增工具概览

### 1. 看板数据操作工具

#### `get_kanban_data` - 获取看板数据
获取看板区块的数据，支持分组、排序和筛选。

**参数：**
- `collectionName` (string, 必需): 集合名称
- `groupField` (string, 可选): 分组字段名称
- `sortField` (string, 可选): 排序字段名称
- `filter` (object, 可选): 筛选条件
- `fields` (array, 可选): 要获取的字段列表
- `appends` (array, 可选): 要关联的字段列表

**示例：**
```javascript
await client.getKanbanData('tasks', {
  groupField: 'status',
  sortField: 'priority_sort',
  filter: { assignee: { $eq: 1 } },
  fields: ['id', 'title', 'status', 'priority'],
  appends: ['assignee', 'project']
});
```

### 2. 看板卡片管理工具

#### `create_kanban_card` - 创建看板卡片
在指定列中创建新的看板卡片。

**参数：**
- `collectionName` (string, 必需): 集合名称
- `cardData` (object, 必需): 卡片数据
- `groupField` (string, 可选): 分组字段名称
- `columnValue` (string|number, 可选): 列值（指定添加到哪一列）

**示例：**
```javascript
await client.createKanbanCard('tasks', {
  title: '新任务',
  description: '任务描述',
  priority: 'high'
}, 'status', 'todo');
```

#### `update_kanban_card` - 更新看板卡片
更新指定的看板卡片数据。

**参数：**
- `collectionName` (string, 必需): 集合名称
- `cardId` (string|number, 必需): 卡片ID
- `updateData` (object, 必需): 更新数据

**示例：**
```javascript
await client.updateKanbanCard('tasks', 123, {
  title: '更新后的任务标题',
  priority: 'urgent'
});
```

#### `delete_kanban_card` - 删除看板卡片
删除指定的看板卡片。

**参数：**
- `collectionName` (string, 必需): 集合名称
- `cardId` (string|number, 必需): 卡片ID

**示例：**
```javascript
await client.deleteKanbanCard('tasks', 123);
```

### 3. 看板卡片移动工具

#### `move_kanban_card` - 移动看板卡片
移动看板卡片到不同的列或位置，支持拖拽操作的API实现。

**参数：**
- `collectionName` (string, 必需): 集合名称
- `sourceId` (string|number, 必需): 要移动的卡片ID
- `targetId` (string|number, 可选): 目标卡片ID（用于精确定位）
- `targetScope` (object, 可选): 目标范围（如 {status: "done"} 移动到完成列）
- `sortField` (string, 可选): 排序字段名称

**示例：**
```javascript
// 移动卡片到"进行中"列
await client.moveKanbanCard('tasks', {
  sourceId: 123,
  targetScope: { status: 'doing' },
  sortField: 'priority_sort'
});

// 移动卡片到指定位置
await client.moveKanbanCard('tasks', {
  sourceId: 123,
  targetId: 456,
  sortField: 'priority_sort'
});
```

### 4. 批量操作工具

#### `batch_update_kanban_cards` - 批量更新看板卡片
批量更新多个看板卡片的数据。

**参数：**
- `collectionName` (string, 必需): 集合名称
- `cardIds` (array, 必需): 卡片ID数组
- `updateData` (object, 必需): 更新数据

**示例：**
```javascript
await client.batchUpdateKanbanCards('tasks', [123, 456, 789], {
  status: 'completed',
  completedAt: new Date().toISOString()
});
```

### 5. 看板配置工具

#### `add_kanban_action` - 添加看板操作按钮
为看板区块添加操作按钮（筛选、新增、自定义请求等）。

**参数：**
- `blockUid` (string, 必需): 看板区块UID
- `actionType` (string, 必需): 操作类型 (filter|addNew|customRequest|export|refresh)
- `title` (string, 必需): 按钮标题
- `icon` (string, 可选): 按钮图标
- `position` (string, 可选): 按钮位置 (left|right)
- `customSettings` (object, 可选): 自定义操作设置

**示例：**
```javascript
// 添加筛选按钮
await client.addKanbanAction('block_uid_123', {
  actionType: 'filter',
  title: '筛选任务',
  icon: 'FilterOutlined',
  position: 'left'
});

// 添加自定义请求按钮
await client.addKanbanAction('block_uid_123', {
  actionType: 'customRequest',
  title: '批量导出',
  customSettings: {
    url: '/api/tasks/export',
    method: 'POST'
  }
});
```

#### `configure_kanban_filter` - 配置看板筛选器
配置看板区块的筛选条件。

**参数：**
- `blockUid` (string, 必需): 看板区块UID
- `filterConditions` (object, 必需): 筛选条件

**示例：**
```javascript
await client.configureKanbanFilter('block_uid_123', {
  assignee: { $eq: 1 },
  priority: { $in: ['high', 'urgent'] }
});
```

#### `configure_kanban_group_field` - 配置看板分组字段
配置看板区块的分组字段。

**参数：**
- `blockUid` (string, 必需): 看板区块UID
- `groupField` (string, 必需): 分组字段名称

**示例：**
```javascript
await client.configureKanbanGroupField('block_uid_123', 'status');
```

#### `configure_kanban_sort_field` - 配置看板排序字段
配置看板区块的排序字段。

**参数：**
- `blockUid` (string, 必需): 看板区块UID
- `sortField` (string, 必需): 排序字段名称

**示例：**
```javascript
await client.configureKanbanSortField('block_uid_123', 'priority_sort');
```

### 6. 字段管理工具

#### `create_sort_field` - 创建排序字段
为集合创建专用的排序字段，用于看板卡片的拖拽排序。

**参数：**
- `collectionName` (string, 必需): 集合名称
- `fieldName` (string, 必需): 排序字段名称
- `fieldTitle` (string, 可选): 排序字段标题
- `scopeKey` (string, 可选): 作用域键

**示例：**
```javascript
await client.createSortField('tasks', {
  fieldName: 'status_sort',
  fieldTitle: '状态排序',
  scopeKey: 'status'
});
```

## 🔧 API 客户端扩展

为了支持这些工具，我们在 `NocoBaseClient` 类中新增了以下方法：

- `getKanbanData()` - 获取看板数据
- `createKanbanCard()` - 创建看板卡片
- `updateKanbanCard()` - 更新看板卡片
- `deleteKanbanCard()` - 删除看板卡片
- `moveKanbanCard()` - 移动看板卡片
- `batchUpdateKanbanCards()` - 批量更新卡片
- `createSortField()` - 创建排序字段
- `updateSortField()` - 更新排序字段

## 🧪 测试验证

运行测试脚本验证功能：

```bash
cd mcp-server-nocobase
npm run build
node scripts/test-kanban-operations.js
```

## 📋 完整工具列表

| 工具名称 | 功能描述 | 对应API |
|---------|----------|---------|
| `get_kanban_data` | 获取看板数据 | `GET /{collection}:list` |
| `create_kanban_card` | 创建看板卡片 | `POST /{collection}:create` |
| `update_kanban_card` | 更新看板卡片 | `PUT /{collection}:update` |
| `delete_kanban_card` | 删除看板卡片 | `DELETE /{collection}:destroy` |
| `move_kanban_card` | 移动看板卡片 | `POST /{collection}:move` |
| `batch_update_kanban_cards` | 批量更新卡片 | 多次 `PUT /{collection}:update` |
| `add_kanban_action` | 添加看板操作按钮 | `POST /uiSchemas:insertAdjacent` |
| `configure_kanban_filter` | 配置看板筛选器 | `PATCH /uiSchemas:patch` |
| `configure_kanban_group_field` | 配置分组字段 | `PATCH /uiSchemas:patch` |
| `configure_kanban_sort_field` | 配置排序字段 | `PATCH /uiSchemas:patch` |
| `create_sort_field` | 创建排序字段 | `POST /collections/{name}/fields:create` |

## 🚀 使用场景

这些工具支持以下看板操作场景：

1. **项目管理看板**：任务状态跟踪、优先级排序、团队协作
2. **销售漏斗看板**：客户状态管理、销售阶段跟踪
3. **内容管理看板**：文章状态管理、发布流程控制
4. **工单处理看板**：工单状态跟踪、处理优先级管理

通过这些工具，AI 客户端可以完整地操作 NocoBase 的看板功能，实现从数据获取到界面配置的全方位控制。
