import axios from 'axios';
import https from 'https';
import testConfig from './test-config.js';

// 修复 Student Management 路由
async function fixStudentManagementRoute() {
  console.log('🔧 修复 Student Management 路由\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    },
    httpsAgent: new https.Agent({
      rejectUnauthorized: false
    })
  });

  try {
    // 1. 找到 Student Management 路由
    console.log('📋 1. 查找 Student Management 路由');
    const routesResponse = await client.get('/desktopRoutes:list');
    const routes = routesResponse.data.data;
    
    const studentRoute = routes.find(r => r.title === 'Student Management');
    if (!studentRoute) {
      console.log('❌ 未找到 Student Management 路由');
      return;
    }
    
    console.log(`✅ 找到 Student Management 路由: ID ${studentRoute.id}`);
    console.log(`   当前 Schema UID: ${studentRoute.schemaUid || 'null'}`);

    // 2. 创建正确的页面 Schema
    console.log('\n📋 2. 创建正确的页面 Schema');
    
    const pageSchemaUid = `page-${Date.now()}-student-mgmt`;
    const tabSchemaUid = `tab-${Date.now()}-student-mgmt`;
    
    // 创建页面 Schema
    const pageSchema = {
      type: 'void',
      'x-component': 'Page',
      'x-app-version': '1.4.0-alpha',
      properties: {
        [tabSchemaUid]: {
          type: 'void',
          'x-component': 'Tabs',
          'x-initializer': 'page:addTab',
          'x-component-props': {},
          properties: {
            tab1: {
              type: 'void',
              title: '{{t("Untitled")}}',
              'x-component': 'Tabs.TabPane',
              'x-designer': 'Tabs.Designer',
              'x-component-props': {},
              properties: {
                grid: {
                  type: 'void',
                  'x-component': 'Grid',
                  'x-initializer': 'page:addBlock',
                  'x-uid': `grid-${Date.now()}`,
                  properties: {}
                }
              }
            }
          }
        }
      }
    };

    // 插入页面 Schema
    try {
      await client.post('/uiSchemas:insertAdjacent', {
        targetUid: 'root',
        position: 'beforeEnd',
        schema: pageSchema,
        'x-uid': pageSchemaUid
      });
      console.log(`   ✅ 创建页面 Schema 成功: ${pageSchemaUid}`);
    } catch (error) {
      console.log(`   ❌ 创建页面 Schema 失败: ${error.response?.data?.message || error.message}`);
      return;
    }

    // 3. 更新路由的 schemaUid
    console.log('\n📋 3. 更新路由的 schemaUid');
    
    try {
      await client.post('/desktopRoutes:update', {
        filterByTk: studentRoute.id,
        values: {
          schemaUid: pageSchemaUid
        }
      });
      console.log(`   ✅ 更新路由 schemaUid 成功: ${pageSchemaUid}`);
    } catch (error) {
      console.log(`   ❌ 更新路由失败: ${error.response?.data?.message || error.message}`);
      return;
    }

    // 4. 验证修复结果
    console.log('\n📋 4. 验证修复结果');
    
    const updatedRoutesResponse = await client.get('/desktopRoutes:list');
    const updatedRoutes = updatedRoutesResponse.data.data;
    const updatedStudentRoute = updatedRoutes.find(r => r.id === studentRoute.id);
    
    if (updatedStudentRoute && updatedStudentRoute.schemaUid) {
      console.log('   ✅ 路由修复成功:');
      console.log(`      - ID: ${updatedStudentRoute.id}`);
      console.log(`      - 标题: "${updatedStudentRoute.title}"`);
      console.log(`      - Schema UID: ${updatedStudentRoute.schemaUid}`);
      console.log(`      - 类型: ${updatedStudentRoute.type}`);
      
      // 验证 Schema 是否存在
      try {
        const schemaResponse = await client.get(`/uiSchemas:getJsonSchema/${updatedStudentRoute.schemaUid}`);
        console.log('      - Schema 状态: ✅ 存在且可访问');
      } catch (error) {
        console.log('      - Schema 状态: ❌ 无法访问');
      }
    } else {
      console.log('   ❌ 路由修复失败');
    }

    // 5. 创建一个新的正确的测试路由
    console.log('\n📋 5. 创建一个新的正确的测试路由');
    
    try {
      const newPageSchemaUid = `page-${Date.now()}-test-correct`;
      const newTabSchemaUid = `tab-${Date.now()}-test-correct`;
      
      // 创建新的页面 Schema
      const newPageSchema = {
        type: 'void',
        'x-component': 'Page',
        'x-app-version': '1.4.0-alpha',
        properties: {
          [newTabSchemaUid]: {
            type: 'void',
            'x-component': 'Tabs',
            'x-initializer': 'page:addTab',
            'x-component-props': {},
            properties: {
              tab1: {
                type: 'void',
                title: '{{t("Untitled")}}',
                'x-component': 'Tabs.TabPane',
                'x-designer': 'Tabs.Designer',
                'x-component-props': {},
                properties: {
                  grid: {
                    type: 'void',
                    'x-component': 'Grid',
                    'x-initializer': 'page:addBlock',
                    'x-uid': `grid-${Date.now()}`,
                    properties: {}
                  }
                }
              }
            }
          }
        }
      };

      // 插入新页面 Schema
      await client.post('/uiSchemas:insertAdjacent', {
        targetUid: 'root',
        position: 'beforeEnd',
        schema: newPageSchema,
        'x-uid': newPageSchemaUid
      });

      // 创建新路由
      const newRouteData = {
        title: 'Test Correct Page',
        type: 'page',
        schemaUid: newPageSchemaUid,
        enableTabs: true,
        enableHeader: true
      };

      const newRouteResponse = await client.post('/desktopRoutes:create', newRouteData);
      const newRoute = newRouteResponse.data.data;
      
      console.log('   ✅ 创建新的正确路由成功:');
      console.log(`      - ID: ${newRoute.id}`);
      console.log(`      - 标题: "${newRoute.title}"`);
      console.log(`      - Schema UID: ${newRoute.schemaUid}`);
      
    } catch (error) {
      console.log(`   ❌ 创建新路由失败: ${error.response?.data?.message || error.message}`);
    }

    console.log('\n🎯 修复完成！');
    console.log('\n📱 请在浏览器中验证:');
    console.log(`🔗 ${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin`);
    console.log('\n💡 现在 Student Management 页面应该可以正常访问了！');

  } catch (error) {
    console.error('❌ 修复失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行修复
fixStudentManagementRoute().catch(console.error);
