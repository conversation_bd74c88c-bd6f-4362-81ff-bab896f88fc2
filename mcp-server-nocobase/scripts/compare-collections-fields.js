#!/usr/bin/env node

// 对比不同集合的字段情况
import { NocoBaseClient } from './dist/client.js';

async function compareCollectionsFields() {
  console.log('🔍 对比不同集合的字段情况...\n');

  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  try {
    // 获取所有集合
    const collections = await client.listCollections();
    console.log(`📋 找到 ${collections.length} 个集合\n`);

    // 检查几个不同的集合
    const collectionsToCheck = ['towns', 'users', 'roles'];
    
    for (const collectionName of collectionsToCheck) {
      const collection = collections.find(c => c.name === collectionName);
      if (!collection) {
        console.log(`❌ 集合 ${collectionName} 不存在\n`);
        continue;
      }

      console.log(`📋 检查集合: ${collectionName} (${collection.title})`);
      console.log(`   autoGenId: ${collection.autoGenId}`);
      console.log(`   createdAt: ${collection.createdAt}`);
      console.log(`   updatedAt: ${collection.updatedAt}`);
      console.log(`   createdBy: ${collection.createdBy}`);
      console.log(`   updatedBy: ${collection.updatedBy}`);

      try {
        const fields = await client.listFields(collectionName);
        console.log(`   字段数量: ${fields.length}`);
        console.log('   字段列表:');
        fields.forEach(field => {
          console.log(`     • ${field.name} (${field.type}) - ${field.interface || 'no interface'}`);
        });

        // 检查一条记录的数据结构
        try {
          const records = await client.listRecords(collectionName, { pageSize: 1 });
          if (records.data.length > 0) {
            const record = records.data[0];
            console.log('   记录中的字段:');
            Object.keys(record).forEach(key => {
              console.log(`     • ${key}: ${typeof record[key]} = ${record[key]}`);
            });
          }
        } catch (recordError) {
          console.log(`   ⚠️  无法获取记录: ${recordError.message}`);
        }
      } catch (fieldError) {
        console.log(`   ❌ 无法获取字段: ${fieldError.message}`);
      }

      console.log('');
    }

  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error.message);
  }
}

compareCollectionsFields().catch(console.error);
