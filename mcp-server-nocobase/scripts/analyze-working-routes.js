import axios from 'axios';
import https from 'https';
import testConfig from './test-config.js';

// 分析正常工作的路由
async function analyzeWorkingRoutes() {
  console.log('🔍 分析正常工作的路由\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    },
    httpsAgent: new https.Agent({
      rejectUnauthorized: false
    })
  });

  try {
    // 1. 获取所有路由
    console.log('📋 1. 获取所有路由');
    const routesResponse = await client.get('/desktopRoutes:list');
    const routes = routesResponse.data.data;
    
    // 找到正常工作的路由（有 schemaUid 的页面路由）
    const workingRoutes = routes.filter(route => 
      route.type === 'page' && route.schemaUid && route.schemaUid !== 'null'
    );
    
    console.log(`✅ 找到 ${workingRoutes.length} 个正常工作的页面路由:`);
    workingRoutes.forEach(route => {
      console.log(`   - ID: ${route.id}, 标题: "${route.title}", Schema: ${route.schemaUid}`);
    });

    // 2. 分析第一个正常路由的 Schema 结构
    if (workingRoutes.length > 0) {
      const firstRoute = workingRoutes[0];
      console.log(`\n📋 2. 分析路由 "${firstRoute.title}" 的 Schema 结构`);
      
      try {
        const schemaResponse = await client.get(`/uiSchemas:getJsonSchema/${firstRoute.schemaUid}`);
        const schema = schemaResponse.data.data;
        
        console.log('   Schema 基本信息:');
        console.log(`   - x-component: ${schema['x-component']}`);
        console.log(`   - x-uid: ${schema['x-uid']}`);
        console.log(`   - type: ${schema.type}`);
        
        if (schema.properties) {
          console.log('   - properties 键:');
          Object.keys(schema.properties).forEach(key => {
            const prop = schema.properties[key];
            console.log(`     * ${key}: ${prop['x-component']} (${prop.type})`);
          });
        }
        
        console.log('\n   完整 Schema 结构:');
        console.log(JSON.stringify(schema, null, 2));
        
      } catch (error) {
        console.log(`   ❌ 获取 Schema 失败: ${error.response?.data?.message || error.message}`);
      }
    }

    // 3. 尝试复制正常路由的结构创建新路由
    if (workingRoutes.length > 0) {
      console.log('\n📋 3. 尝试复制正常路由的结构创建新路由');
      
      const templateRoute = workingRoutes[0];
      
      try {
        // 获取模板路由的 Schema
        const templateSchemaResponse = await client.get(`/uiSchemas:getJsonSchema/${templateRoute.schemaUid}`);
        const templateSchema = templateSchemaResponse.data.data;
        
        // 创建新的 Schema UID
        const newSchemaUid = `page-${Date.now()}-copied`;
        
        // 复制 Schema 结构，但使用新的 UID
        const newSchema = {
          ...templateSchema,
          'x-uid': newSchemaUid
        };
        
        // 如果有 properties，也需要更新其中的 UID
        if (newSchema.properties) {
          Object.keys(newSchema.properties).forEach(key => {
            if (newSchema.properties[key]['x-uid']) {
              newSchema.properties[key]['x-uid'] = `${key}-${Date.now()}`;
            }
            
            // 递归处理嵌套的 properties
            if (newSchema.properties[key].properties) {
              Object.keys(newSchema.properties[key].properties).forEach(subKey => {
                if (newSchema.properties[key].properties[subKey]['x-uid']) {
                  newSchema.properties[key].properties[subKey]['x-uid'] = `${subKey}-${Date.now()}`;
                }
              });
            }
          });
        }
        
        // 插入新的 Schema
        await client.post('/uiSchemas:insertAdjacent', {
          targetUid: 'root',
          position: 'beforeEnd',
          schema: newSchema,
          'x-uid': newSchemaUid
        });
        
        console.log(`   ✅ 复制 Schema 成功: ${newSchemaUid}`);
        
        // 创建使用这个 Schema 的路由
        const newRouteData = {
          title: 'Copied Working Route',
          type: 'page',
          schemaUid: newSchemaUid
        };

        const newRouteResponse = await client.post('/desktopRoutes:create', newRouteData);
        const newRoute = newRouteResponse.data.data;
        
        console.log('   ✅ 创建新路由成功:');
        console.log(`      - ID: ${newRoute.id}`);
        console.log(`      - 标题: "${newRoute.title}"`);
        console.log(`      - Schema UID: ${newRoute.schemaUid}`);
        
      } catch (error) {
        console.log(`   ❌ 复制路由失败: ${error.response?.data?.message || error.message}`);
        if (error.response?.data) {
          console.log('   错误详情:', JSON.stringify(error.response.data, null, 2));
        }
      }
    }

    // 4. 测试访问新创建的路由
    console.log('\n📋 4. 验证路由访问');
    
    const finalRoutesResponse = await client.get('/desktopRoutes:list');
    const finalRoutes = finalRoutesResponse.data.data;
    
    const newRoutes = finalRoutes.filter(route => 
      route.title.includes('Student') || route.title.includes('Copied') || route.title.includes('Management')
    );
    
    console.log('   新创建的路由:');
    newRoutes.forEach(route => {
      const status = route.schemaUid ? '✅' : '❌';
      const url = route.schemaUid ? 
        `${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/${route.schemaUid}` :
        `${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/null`;
      
      console.log(`   ${status} "${route.title}" - ${url}`);
    });

    console.log('\n🎯 分析完成！');
    console.log('\n💡 关键发现:');
    console.log('   1. 正常工作的路由都有有效的 schemaUid');
    console.log('   2. Schema 必须先创建并插入到 UI Schema 系统中');
    console.log('   3. 路由创建时必须提供已存在的 schemaUid');
    console.log('   4. NocoBase 不会自动为路由创建 Schema');

  } catch (error) {
    console.error('❌ 分析失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行分析
analyzeWorkingRoutes().catch(console.error);
