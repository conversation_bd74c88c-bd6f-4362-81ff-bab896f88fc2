import axios from 'axios';
import https from 'https';
import testConfig from './test-config.js';

// 清理除了 users 和 roles 以外的所有 collections
async function cleanupCollections() {
  console.log('🧹 清理除了 users 和 roles 以外的所有 collections\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    },
    httpsAgent: new https.Agent({
      rejectUnauthorized: false
    })
  });

  try {
    // 1. 获取所有 collections
    console.log('📋 1. 获取所有现有 collections');
    const collectionsResponse = await client.get('/collections:list');
    const collections = collectionsResponse.data.data;
    
    console.log(`✅ 找到 ${collections.length} 个 collections:`);
    collections.forEach(collection => {
      console.log(`   - 名称: ${collection.name}, 标题: "${collection.title || 'N/A'}"`);
    });
    
    // 2. 识别要保留和删除的 collections
    console.log('\n📋 2. 识别要保留和删除的 collections');
    
    const keepCollections = ['users', 'roles'];
    const collectionsToKeep = collections.filter(collection => 
      keepCollections.includes(collection.name)
    );
    const collectionsToDelete = collections.filter(collection => 
      !keepCollections.includes(collection.name)
    );
    
    console.log(`✅ 要保留的 collections (${collectionsToKeep.length} 个):`);
    collectionsToKeep.forEach(collection => {
      console.log(`   - 保留: ${collection.name} - "${collection.title || 'N/A'}"`);
    });
    
    console.log(`🗑️ 要删除的 collections (${collectionsToDelete.length} 个):`);
    collectionsToDelete.forEach(collection => {
      console.log(`   - 删除: ${collection.name} - "${collection.title || 'N/A'}"`);
    });
    
    if (collectionsToDelete.length === 0) {
      console.log('ℹ️ 没有需要删除的 collections');
      return;
    }
    
    // 3. 删除不需要的 collections
    console.log('\n📋 3. 删除不需要的 collections');
    
    for (const collection of collectionsToDelete) {
      try {
        console.log(`🗑️ 删除 collection: ${collection.name} - "${collection.title || 'N/A'}"`);
        
        // 删除 collection
        await client.post(`/collections:destroy?filterByTk=${collection.name}`);
        console.log(`   ✅ 删除 collection 成功: ${collection.name}`);
        
      } catch (error) {
        console.log(`   ❌ 删除 collection 失败: ${collection.name}, 错误: ${error.response?.data?.message || error.message}`);
        
        // 如果是因为有关联关系导致删除失败，尝试强制删除
        if (error.response?.data?.message?.includes('foreign key') || 
            error.response?.data?.message?.includes('constraint') ||
            error.response?.data?.message?.includes('关联')) {
          try {
            console.log(`   🔄 尝试强制删除 collection: ${collection.name}`);
            await client.post(`/collections:destroy`, {
              filter: { name: collection.name },
              cascade: true
            });
            console.log(`   ✅ 强制删除 collection 成功: ${collection.name}`);
          } catch (forceError) {
            console.log(`   ❌ 强制删除也失败: ${collection.name}, 错误: ${forceError.response?.data?.message || forceError.message}`);
          }
        }
      }
    }
    
    // 4. 验证清理结果
    console.log('\n📋 4. 验证清理结果');
    
    const finalCollectionsResponse = await client.get('/collections:list');
    const finalCollections = finalCollectionsResponse.data.data;
    
    console.log(`✅ 清理后剩余 ${finalCollections.length} 个 collections:`);
    finalCollections.forEach(collection => {
      console.log(`   - 名称: ${collection.name}, 标题: "${collection.title || 'N/A'}"`);
    });
    
    // 验证 users 和 roles 是否还在
    const usersCollection = finalCollections.find(c => c.name === 'users');
    const rolesCollection = finalCollections.find(c => c.name === 'roles');
    
    if (usersCollection && rolesCollection) {
      console.log('🎉 users 和 roles collections 已保留');
    } else {
      if (!usersCollection) console.log('⚠️ users collection 似乎被意外删除了');
      if (!rolesCollection) console.log('⚠️ roles collection 似乎被意外删除了');
    }
    
    console.log('\n🎯 Collections 清理完成！');
    console.log('\n📱 请在浏览器中验证:');
    console.log(`🔗 ${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin`);
    console.log('\n💡 现在应该只剩下:');
    console.log('   - users collection');
    console.log('   - roles collection');
    console.log('\n🚀 其他 collections 已被清理！');

  } catch (error) {
    console.error('❌ 清理失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行清理
cleanupCollections().catch(console.error);
