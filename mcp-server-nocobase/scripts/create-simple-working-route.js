import axios from 'axios';
import https from 'https';
import testConfig from './test-config.js';

// 创建简单的工作路由
async function createSimpleWorkingRoute() {
  console.log('✨ 创建简单的工作路由\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    },
    httpsAgent: new https.Agent({
      rejectUnauthorized: false
    })
  });

  try {
    // 1. 创建最简单的 Schema（模仿 courses 路由的结构）
    console.log('📋 1. 创建简单的页面 Schema');
    
    const schemaUid = `page-${Date.now()}-simple`;
    const schemaName = `schema-${Date.now()}`;
    
    // 最简单的页面 Schema
    const simpleSchema = {
      type: "void",
      "x-component": "Page",
      name: schema<PERSON>ame,
      "x-uid": schemaUid,
      "x-async": false
    };
    
    try {
      // 使用 create 方法而不是 insertAdjacent
      const schemaResponse = await client.post('/uiSchemas:create', {
        values: simpleSchema
      });
      
      console.log(`   ✅ 创建简单 Schema 成功: ${schemaUid}`);
      
    } catch (error) {
      console.log(`   ❌ 创建 Schema 失败: ${error.response?.data?.message || error.message}`);
      
      // 尝试另一种方法：直接插入
      try {
        await client.post('/uiSchemas:insert', {
          schema: simpleSchema
        });
        console.log(`   ✅ 插入简单 Schema 成功: ${schemaUid}`);
      } catch (insertError) {
        console.log(`   ❌ 插入 Schema 也失败: ${insertError.response?.data?.message || insertError.message}`);
        return;
      }
    }

    // 2. 创建使用这个 Schema 的路由
    console.log('\n📋 2. 创建使用 Schema 的路由');
    
    const routeData = {
      title: 'Test Working Page',
      type: 'page',
      schemaUid: schemaUid
    };

    try {
      const routeResponse = await client.post('/desktopRoutes:create', routeData);
      const route = routeResponse.data.data;
      
      console.log('   ✅ 创建路由成功:');
      console.log(`      - ID: ${route.id}`);
      console.log(`      - 标题: "${route.title}"`);
      console.log(`      - Schema UID: ${route.schemaUid}`);
      console.log(`      - 访问 URL: ${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/${route.schemaUid}`);
      
    } catch (error) {
      console.log(`   ❌ 创建路由失败: ${error.response?.data?.message || error.message}`);
    }

    // 3. 尝试更直接的方法：复制现有的工作路由
    console.log('\n📋 3. 尝试复制现有工作路由的方法');
    
    // 获取 courses 路由的详细信息
    const routesResponse = await client.get('/desktopRoutes:list');
    const routes = routesResponse.data.data;
    const coursesRoute = routes.find(r => r.title === 'courses');
    
    if (coursesRoute) {
      console.log(`   找到 courses 路由: ${coursesRoute.schemaUid}`);
      
      // 创建一个新的简单 Schema UID
      const newSchemaUid = `page-${Date.now()}-copy`;
      
      // 直接创建路由，使用一个新的 UID（让 NocoBase 处理 Schema 创建）
      const copyRouteData = {
        title: 'Working Copy Page',
        type: 'page'
        // 不提供 schemaUid，看看 NocoBase 是否会自动创建
      };

      try {
        const copyRouteResponse = await client.post('/desktopRoutes:create', copyRouteData);
        const copyRoute = copyRouteResponse.data.data;
        
        console.log('   ✅ 创建复制路由成功:');
        console.log(`      - ID: ${copyRoute.id}`);
        console.log(`      - 标题: "${copyRoute.title}"`);
        console.log(`      - Schema UID: ${copyRoute.schemaUid || 'null'}`);
        
        // 如果 NocoBase 没有自动创建 Schema，我们手动添加一个
        if (!copyRoute.schemaUid) {
          console.log('   尝试手动更新路由的 schemaUid...');
          
          const updateSchemaUid = `page-${Date.now()}-manual`;
          
          // 先创建一个简单的 Schema
          const manualSchema = {
            type: "void",
            "x-component": "Page",
            "x-uid": updateSchemaUid
          };
          
          try {
            // 尝试直接插入到 uiSchemas 表
            await client.post('/uiSchemas', {
              ...manualSchema
            });
            
            // 更新路由
            await client.post('/desktopRoutes:update', {
              filterByTk: copyRoute.id,
              values: {
                schemaUid: updateSchemaUid
              }
            });
            
            console.log(`   ✅ 手动更新成功: ${updateSchemaUid}`);
            
          } catch (manualError) {
            console.log(`   ❌ 手动更新失败: ${manualError.response?.data?.message || manualError.message}`);
          }
        }
        
      } catch (error) {
        console.log(`   ❌ 创建复制路由失败: ${error.response?.data?.message || error.message}`);
      }
    }

    // 4. 验证最终结果
    console.log('\n📋 4. 验证最终结果');
    
    const finalRoutesResponse = await client.get('/desktopRoutes:list');
    const finalRoutes = finalRoutesResponse.data.data;
    
    const testRoutes = finalRoutes.filter(route => 
      route.title.includes('Test') || route.title.includes('Working') || route.title.includes('Student')
    );
    
    console.log('   测试路由状态:');
    testRoutes.forEach(route => {
      const status = route.schemaUid && route.schemaUid !== 'null' ? '✅' : '❌';
      const url = route.schemaUid && route.schemaUid !== 'null' ? 
        `${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/${route.schemaUid}` :
        `${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/null`;
      
      console.log(`   ${status} "${route.title}" (ID: ${route.id})`);
      console.log(`      Schema UID: ${route.schemaUid || 'null'}`);
      console.log(`      访问 URL: ${url}`);
    });

    console.log('\n🎯 创建完成！');
    console.log('\n📱 请在浏览器中测试:');
    console.log(`🔗 ${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin`);

  } catch (error) {
    console.error('❌ 创建失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行创建
createSimpleWorkingRoute().catch(console.error);
