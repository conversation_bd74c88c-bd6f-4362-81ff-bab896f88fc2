#!/usr/bin/env node

// 更新镇街集合以符合命名规范
import { NocoBaseClient } from './dist/client.js';

async function updateTownsCollection() {
  console.log('🔄 更新镇街集合以符合命名规范...\n');

  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  try {
    // 1. 更新集合的基本信息
    console.log('📋 更新集合基本信息');
    const updatedCollection = await client.updateCollection('towns', {
      title: 'Towns',
      description: 'Administrative divisions at town and street level'
    });
    console.log(`✅ 成功更新集合信息:`);
    console.log(`   名称: ${updatedCollection.name}`);
    console.log(`   标题: ${updatedCollection.title}`);
    console.log(`   描述: ${updatedCollection.description || '无描述'}`);
    console.log();

    // 2. 更新字段的UI标题为英文
    console.log('📋 更新字段UI标题为英文');
    
    // 更新city字段
    try {
      await client.updateField('towns', 'city', {
        uiSchema: {
          type: 'string',
          title: 'City',
          'x-component': 'Input',
          required: true
        }
      });
      console.log('✅ 更新city字段标题: City');
    } catch (error) {
      console.log(`⚠️  更新city字段失败: ${error.message}`);
    }

    // 更新district字段
    try {
      await client.updateField('towns', 'district', {
        uiSchema: {
          type: 'string',
          title: 'District',
          'x-component': 'Input',
          required: true
        }
      });
      console.log('✅ 更新district字段标题: District');
    } catch (error) {
      console.log(`⚠️  更新district字段失败: ${error.message}`);
    }

    // 更新name字段
    try {
      await client.updateField('towns', 'name', {
        uiSchema: {
          type: 'string',
          title: 'Town Name',
          'x-component': 'Input',
          required: true
        }
      });
      console.log('✅ 更新name字段标题: Town Name');
    } catch (error) {
      console.log(`⚠️  更新name字段失败: ${error.message}`);
    }

    console.log();

    // 3. 验证更新结果
    console.log('🔍 验证更新结果:');
    const collection = await client.getCollection('towns');
    console.log(`集合标题: ${collection.title}`);
    console.log(`集合描述: ${collection.description || '无描述'}`);

    const fields = await client.listFields('towns');
    console.log('\n字段信息:');
    fields.forEach(field => {
      console.log(`   • ${field.name} - ${field.uiSchema?.title || '无标题'}`);
    });

    // 4. 创建一条测试记录验证功能
    console.log('\n📋 创建测试记录验证功能:');
    try {
      const testRecord = await client.createRecord('towns', {
        city: 'Shenzhen',
        district: 'Nanshan District',
        name: 'Shekou Street'
      });
      console.log(`✅ 创建测试记录成功: ${testRecord.city} ${testRecord.district} ${testRecord.name}`);
    } catch (error) {
      console.log(`⚠️  创建测试记录失败: ${error.message}`);
    }

    console.log('\n🎉 镇街集合更新完成！');
    console.log('\n📊 更新摘要:');
    console.log('   ✅ 集合标题更新为英文: Towns');
    console.log('   ✅ 集合描述更新为英文: Administrative divisions at town and street level');
    console.log('   ✅ 字段标题更新为英文: City, District, Town Name');
    console.log('   ✅ 符合命名规范标准');

  } catch (error) {
    console.error('❌ 更新过程中发生错误:', error.message);
  }
}

updateTownsCollection().catch(console.error);
