---
type: "always_apply"
---

# NocoBase Collection Naming Standards

## 📋 Overview

This document establishes naming conventions for NocoBase collections to ensure consistency, maintainability, and internationalization support.

## 🎯 Naming Rules

### Collection Name Rules
- **Use plural nouns**: `users`, `towns`, `products`, `categories`
- **Lowercase with underscores if needed**: `user_profiles`, `order_items`
- **Start with a letter**: Must begin with `a-z`
- **Length**: 3-50 characters
- **Characters**: Only letters, numbers, and underscores
- **Avoid**: Verbs, singular forms, special characters

### Display Title Rules
- **Use English**: `"Users"`, `"Towns"`, `"Product Categories"`
- **Capitalize first letter**: Each word should start with capital letter
- **Length**: 1-100 characters
- **Avoid**: Chinese characters, all lowercase, all uppercase

### Description Rules
- **Use English**: Clear, descriptive English text
- **Describe purpose**: Explain what the collection stores and manages
- **Length**: 10-500 characters
- **Format**: Complete sentences preferred

### Category Rules
- **Use English categories**: `["Administrative", "Geography"]`
- **Standard categories**: Use predefined categories when possible
- **Multiple categories**: Collections can belong to multiple categories

## 📚 Standard Categories

### Functional Categories
- **User Management**: `users`, `roles`, `permissions`, `user_profiles`
- **Content Management**: `articles`, `pages`, `media`, `comments`
- **Commerce**: `products`, `orders`, `customers`, `payments`
- **Administrative**: `provinces`, `cities`, `districts`, `towns`, `villages`
- **System**: `logs`, `settings`, `configurations`, `notifications`
- **Workflow**: `workflows`, `tasks`, `approvals`, `processes`

### Domain Categories
- **Geography**: Location and administrative division related collections
- **Organization**: Company structure and department related collections
- **Analytics**: Data analysis and reporting related collections
- **Communication**: Messaging and notification related collections

## ✅ Good Examples

### Administrative Collections
```javascript
{
  name: 'provinces',
  title: 'Provinces',
  description: 'Provincial administrative divisions with codes and hierarchical information',
  category: ['Administrative', 'Geography']
}

{
  name: 'cities',
  title: 'Cities',
  description: 'City-level administrative divisions linked to provinces',
  category: ['Administrative', 'Geography']
}

{
  name: 'districts',
  title: 'Districts',
  description: 'District and county level administrative divisions',
  category: ['Administrative', 'Geography']
}

{
  name: 'towns',
  title: 'Towns',
  description: 'Town and street level administrative divisions',
  category: ['Administrative', 'Geography']
}
```

### User Management Collections
```javascript
{
  name: 'users',
  title: 'Users',
  description: 'System users with authentication and profile information',
  category: ['User Management']
}

{
  name: 'user_profiles',
  title: 'User Profiles',
  description: 'Extended user profile information and preferences',
  category: ['User Management']
}

{
  name: 'roles',
  title: 'Roles',
  description: 'User roles and permission groups for access control',
  category: ['User Management', 'System']
}
```

### Commerce Collections
```javascript
{
  name: 'products',
  title: 'Products',
  description: 'Product catalog with specifications and pricing information',
  category: ['Commerce']
}

{
  name: 'orders',
  title: 'Orders',
  description: 'Customer orders with items, status, and payment information',
  category: ['Commerce']
}

{
  name: 'customers',
  title: 'Customers',
  description: 'Customer information and contact details',
  category: ['Commerce', 'User Management']
}
```

## ❌ Examples to Avoid

### Poor Collection Names
```javascript
// ❌ Singular form
{ name: 'user', title: 'User' }
// ✅ Should be
{ name: 'users', title: 'Users' }

// ❌ Using verbs
{ name: 'createOrder', title: 'Create Order' }
// ✅ Should be
{ name: 'orders', title: 'Orders' }

// ❌ Mixed case
{ name: 'UserProfiles', title: 'userprofiles' }
// ✅ Should be
{ name: 'user_profiles', title: 'User Profiles' }
```

### Poor Titles and Descriptions
```javascript
// ❌ Chinese characters
{
  name: 'towns',
  title: '镇街集合',
  description: '管理镇街信息的集合'
}

// ✅ Should be
{
  name: 'towns',
  title: 'Towns',
  description: 'Administrative divisions at town and street level'
}
```

## 🔧 Implementation

### Using MCP Tools
The `create_collection` MCP tool now includes validation for these naming standards:

```javascript
// The tool will provide warnings and suggestions
await mcpClient.callTool('create_collection', {
  name: 'town',  // Will suggest 'towns'
  title: '镇街',  // Will warn about Chinese characters
  description: '镇街信息'  // Will warn about Chinese characters
});
```

### Using Client Methods
```javascript
// Use the enhanced client method
await client.createCollectionWithDefaults({
  name: 'towns',
  title: 'Towns',
  description: 'Administrative divisions at town and street level',
  category: ['Administrative', 'Geography'],
  // ... other options
});
```

## 📊 Validation Rules

The system will automatically validate:
1. **Name format**: Lowercase, starts with letter, contains only valid characters
2. **Length limits**: Name (3-50), title (1-100), description (10-500)
3. **Character validation**: English characters preferred for title and description
4. **Naming suggestions**: Plural forms, proper capitalization

## 🎯 Migration Guide

For existing collections that don't follow these standards:

1. **Update titles and descriptions** to use English
2. **Add appropriate categories** for better organization
3. **Consider renaming** collections if they don't follow naming conventions
4. **Update documentation** to reflect new standards

## 📝 Best Practices Summary

1. **Always use plural nouns** for collection names
2. **Use English** for titles, descriptions, and categories
3. **Be descriptive** in descriptions - explain the purpose
4. **Use standard categories** for consistency
5. **Validate naming** before creating collections
6. **Document collections** with clear, English descriptions
7. **Consider internationalization** from the start