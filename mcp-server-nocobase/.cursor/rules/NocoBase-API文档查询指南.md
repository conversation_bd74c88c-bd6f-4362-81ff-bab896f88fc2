---
type: "always_apply"
---

# NocoBase API 文档查询指南

## 1. 访问 API 文档

### 1.1 界面访问方式

**主要访问路径：**
- **管理界面入口：** `http://localhost:13000/admin/settings/api-doc`
- **独立文档页面：** `http://localhost:13000/api-documentation`

### 1.2 API 端点访问

**获取所有可用文档：**
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:13000/api/swagger:getUrls
```

**获取分类文档：**
```bash
# 完整 API 文档
GET /api/swagger:get

# 核心 API
GET /api/swagger:get?ns=core

# 插件 API
GET /api/swagger:get?ns=plugins

# 集合 API
GET /api/swagger:get?ns=collections

# 特定集合
GET /api/swagger:get?ns=collections/users
```

## 2. 文档分类结构

### 2.1 核心分类

| 分类 | 路径 | 描述 |
|------|------|------|
| `core` | `?ns=core` | 核心 API（认证、用户管理等） |
| `plugins` | `?ns=plugins` | 所有插件 API |
| `collections` | `?ns=collections` | 所有自定义集合 API |
| `plugins/{name}` | `?ns=plugins/{name}` | 特定插件 API |
| `collections/{name}` | `?ns=collections/{name}` | 特定集合 API |

### 2.2 Collections API 结构

**基础操作：**
- `{collectionName}:list` - 获取列表
- `{collectionName}:get` - 获取详情
- `{collectionName}:create` - 创建记录
- `{collectionName}:update` - 更新记录
- `{collectionName}:destroy` - 删除记录

**关联操作：**
- `{collectionName}.{association}:get` - 获取关联数据
- `{collectionName}.{association}:list` - 获取关联列表
- `{collectionName}.{association}:create` - 创建关联
- `{collectionName}.{association}:update` - 更新关联
- `{collectionName}.{association}:destroy` - 删除关联

## 3. 查询参数详解

### 3.1 通用参数

| 参数 | 类型 | 描述 | 示例 |
|------|------|------|------|
| `page` | integer | 页码 | `page=1` |
| `pageSize` | integer | 页面大小 | `pageSize=20` |
| `sort` | array/string | 排序 | `sort=-createdAt` |
| `fields` | array/string | 选择字段 | `fields=id,name,email` |
| `appends` | array/string | 包含关联 | `appends=profile,roles` |
| `filter` | object | 过滤条件 | 见下方过滤语法 |

### 3.2 过滤语法

**基础过滤：**
```json
{
  "name": "张三",
  "status": "active"
}
```

**复杂过滤：**
```json
{
  "and": [
    { "age": { "gt": 18 } },
    { "status": { "in": ["active", "pending"] } }
  ],
  "or": [
    { "name": { "like": "%张%" } },
    { "email": { "like": "%zhang%" } }
  ]
}
```

**操作符：**
- `eq` - 等于
- `ne` - 不等于
- `gt` - 大于
- `gte` - 大于等于
- `lt` - 小于
- `lte` - 小于等于
- `like` - 模糊匹配
- `in` - 包含在数组中
- `notIn` - 不包含在数组中

## 4. 认证方式

### 4.1 获取认证令牌

```bash
# 登录获取 token
curl -X POST http://localhost:13000/api/auth:login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "admin123"}'
```

### 4.2 使用认证

```bash
# 在请求头中添加认证信息
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:13000/api/swagger:get
```

## 5. 实用查询示例

### 5.1 基础查询

```bash
# 获取用户列表
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:13000/api/users:list?page=1&pageSize=10"

# 获取特定用户
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:13000/api/users:get?filterByTk=1"
```

### 5.2 复杂过滤

```bash
# 复杂过滤查询
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     "http://localhost:13000/api/users:list" \
     -d '{
       "filter": {
         "and": [
           { "status": "active" },
           { "age": { "gte": 18 } }
         ],
         "name": { "like": "%张%" }
       },
       "sort": ["-createdAt"],
       "fields": ["id", "name", "email", "createdAt"]
     }'
```

### 5.3 关联查询

```bash
# 包含关联数据
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:13000/api/users:list?appends=profile,roles"
```

## 6. 常用 API 端点速查

### 6.1 系统核心 API

| 端点 | 方法 | 描述 |
|------|------|------|
| `/auth:login` | POST | 用户登录 |
| `/auth:logout` | POST | 用户登出 |
| `/auth:check` | GET | 检查登录状态 |
| `/users:list` | GET | 获取用户列表 |
| `/users:get` | GET | 获取用户详情 |
| `/roles:list` | GET | 获取角色列表 |
| `/collections:list` | GET | 获取集合列表 |

### 6.2 Collections API 模板

```bash
# 列表查询
GET /api/{collectionName}:list

# 详情查询
GET /api/{collectionName}:get?filterByTk={id}

# 创建记录
POST /api/{collectionName}:create

# 更新记录
POST /api/{collectionName}:update

# 删除记录
POST /api/{collectionName}:destroy
```

## 7. 开发技巧

### 7.1 使用 Swagger UI

1. **在线测试：** 直接在 Swagger UI 中测试 API
2. **自动认证：** 界面会自动添加认证头
3. **参数提示：** 实时显示参数说明
4. **响应示例：** 提供响应格式示例

### 7.2 调试技巧

1. **查看请求详情：** 使用浏览器开发者工具
2. **检查响应格式：** 关注 `data` 和 `meta` 字段
3. **错误处理：** 查看 `status` 和 `message` 字段
4. **性能优化：** 合理使用 `fields` 和 `appends` 参数

### 7.3 最佳实践

1. **分页查询：** 总是使用 `page` 和 `pageSize`
2. **字段选择：** 使用 `fields` 减少数据传输
3. **合理过滤：** 在服务端过滤而非客户端
4. **关联优化：** 按需使用 `appends`

## 8. 常见问题解答

### 8.1 认证问题

**问题：** 401 未授权错误
**解决：** 检查 Token 是否正确，是否已过期

### 8.2 权限问题

**问题：** 403 禁止访问
**解决：** 检查用户角色是否有相应权限

### 8.3 参数问题

**问题：** 400 参数错误
**解决：** 检查参数格式，特别是 JSON 格式

### 8.4 关联查询

**问题：** 关联数据为空
**解决：** 检查关联名称是否正确，用户是否有权限访问关联数据